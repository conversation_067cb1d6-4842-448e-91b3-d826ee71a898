[["Map", 1, 2, 9, 10, 114, 115], "meta::meta", ["Map", 3, 4, 5, 6, 7, 8], "astro-version", "5.8.2", "content-config-digest", "ffbf591254b1f6e4", "astro-config-digest", "{\"root\":{},\"srcDir\":{},\"publicDir\":{},\"outDir\":{},\"cacheDir\":{},\"site\":\"https://bagusfarisa-astro.vercel.app\",\"compressHTML\":true,\"base\":\"/\",\"trailingSlash\":\"ignore\",\"output\":\"server\",\"scopedStyleStrategy\":\"attribute\",\"build\":{\"format\":\"directory\",\"client\":{},\"server\":{},\"assets\":\"_astro\",\"serverEntry\":\"entry.mjs\",\"redirects\":false,\"inlineStylesheets\":\"auto\",\"concurrency\":1},\"server\":{\"open\":false,\"host\":false,\"port\":4321,\"streaming\":true,\"allowedHosts\":[]},\"redirects\":{},\"image\":{\"endpoint\":{\"route\":\"/_image\"},\"service\":{\"entrypoint\":\"astro/assets/services/sharp\",\"config\":{}},\"domains\":[],\"remotePatterns\":[],\"experimentalDefaultStyles\":true},\"devToolbar\":{\"enabled\":true},\"markdown\":{\"syntaxHighlight\":{\"type\":\"shiki\",\"excludeLangs\":[\"math\"]},\"shikiConfig\":{\"langs\":[],\"langAlias\":{},\"theme\":\"github-dark\",\"themes\":{},\"wrap\":false,\"transformers\":[]},\"remarkPlugins\":[],\"rehypePlugins\":[],\"remarkRehype\":{},\"gfm\":true,\"smartypants\":true},\"security\":{\"checkOrigin\":true},\"env\":{\"schema\":{},\"validateSecrets\":false},\"experimental\":{\"clientPrerender\":false,\"contentIntellisense\":false,\"responsiveImages\":false,\"headingIdCompat\":false,\"preserveScriptOrder\":false},\"legacy\":{\"collections\":false}}", "projects", ["Map", 11, 12, 65, 66], "portfolio-website", {"id": 11, "data": 13, "body": 28, "filePath": 29, "digest": 30, "rendered": 31, "legacyId": 64}, {"title": 14, "description": 15, "image": 16, "technologies": 17, "liveUrl": 23, "githubUrl": 24, "featured": 25, "publishDate": 26, "status": 27}, "Portfolio Website with Astro & Decap CMS", "A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management.", "/images/portfolio-preview.jpg", [18, 19, 20, 21, 22], "Astro", "TypeScript", "Decap CMS", "CSS", "Vercel", "https://bagusfarisa-astro.vercel.app", "https://github.com/bagusfarisa/bagusfarisa-astro", true, ["Date", "2024-06-05T10:00:00.000Z"], "completed", "# Portfolio Website with Astro & Decap CMS\n\nThis portfolio website showcases my projects and blog posts, built with modern web technologies for optimal performance and easy content management.\n\n## Features\n\n- **Static Site Generation**: Built with Astro for lightning-fast performance\n- **Content Management**: Integrated with Decap CMS for easy content editing\n- **Responsive Design**: Mobile-first approach ensuring great experience on all devices\n- **SEO Optimized**: Proper meta tags, structured data, and performance optimization\n- **Modern Stack**: TypeScript, CSS custom properties, and modern JavaScript\n\n## Technical Implementation\n\n### Astro Framework\nThe site leverages Astro's unique approach to static site generation, allowing for:\n- Zero JavaScript by default\n- Component-based architecture\n- Content collections for type-safe content management\n- Excellent performance out of the box\n\n### Decap CMS Integration\nContent management is handled through Decap CMS, providing:\n- Git-based workflow\n- Rich text editing\n- Media management\n- Preview functionality\n- User-friendly interface for non-technical users\n\n### Deployment\nThe site is deployed on Vercel with:\n- Automatic deployments from Git\n- Edge network distribution\n- Optimized build process\n- Environment variable management\n\n## Development Process\n\n1. **Planning**: Defined the site structure and content requirements\n2. **Design**: Created a clean, professional design system\n3. **Development**: Built components and pages with Astro\n4. **CMS Setup**: Configured Decap CMS for content management\n5. **Testing**: Ensured cross-browser compatibility and performance\n6. **Deployment**: Set up CI/CD pipeline with Vercel\n\nThis project demonstrates my ability to work with modern web technologies while maintaining focus on performance, accessibility, and user experience.", "src/content/projects/portfolio-website.md", "004cd52c22d6ffc5", {"html": 32, "metadata": 33}, "<h1 id=\"portfolio-website-with-astro--decap-cms\">Portfolio Website with Astro &#x26; Decap CMS</h1>\n<p>This portfolio website showcases my projects and blog posts, built with modern web technologies for optimal performance and easy content management.</p>\n<h2 id=\"features\">Features</h2>\n<ul>\n<li><strong>Static Site Generation</strong>: Built with Astro for lightning-fast performance</li>\n<li><strong>Content Management</strong>: Integrated with Decap CMS for easy content editing</li>\n<li><strong>Responsive Design</strong>: Mobile-first approach ensuring great experience on all devices</li>\n<li><strong>SEO Optimized</strong>: Proper meta tags, structured data, and performance optimization</li>\n<li><strong>Modern Stack</strong>: TypeScript, CSS custom properties, and modern JavaScript</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<h3 id=\"astro-framework\">Astro Framework</h3>\n<p>The site leverages Astro’s unique approach to static site generation, allowing for:</p>\n<ul>\n<li>Zero JavaScript by default</li>\n<li>Component-based architecture</li>\n<li>Content collections for type-safe content management</li>\n<li>Excellent performance out of the box</li>\n</ul>\n<h3 id=\"decap-cms-integration\">Decap CMS Integration</h3>\n<p>Content management is handled through Decap CMS, providing:</p>\n<ul>\n<li>Git-based workflow</li>\n<li>Rich text editing</li>\n<li>Media management</li>\n<li>Preview functionality</li>\n<li>User-friendly interface for non-technical users</li>\n</ul>\n<h3 id=\"deployment\">Deployment</h3>\n<p>The site is deployed on Vercel with:</p>\n<ul>\n<li>Automatic deployments from Git</li>\n<li>Edge network distribution</li>\n<li>Optimized build process</li>\n<li>Environment variable management</li>\n</ul>\n<h2 id=\"development-process\">Development Process</h2>\n<ol>\n<li><strong>Planning</strong>: Defined the site structure and content requirements</li>\n<li><strong>Design</strong>: Created a clean, professional design system</li>\n<li><strong>Development</strong>: Built components and pages with Astro</li>\n<li><strong>CMS Setup</strong>: Configured Decap CMS for content management</li>\n<li><strong>Testing</strong>: Ensured cross-browser compatibility and performance</li>\n<li><strong>Deployment</strong>: Set up CI/CD pipeline with Vercel</li>\n</ol>\n<p>This project demonstrates my ability to work with modern web technologies while maintaining focus on performance, accessibility, and user experience.</p>", {"headings": 34, "localImagePaths": 58, "remoteImagePaths": 59, "frontmatter": 60, "imagePaths": 63}, [35, 38, 42, 45, 49, 52, 55], {"depth": 36, "slug": 37, "text": 14}, 1, "portfolio-website-with-astro--decap-cms", {"depth": 39, "slug": 40, "text": 41}, 2, "features", "Features", {"depth": 39, "slug": 43, "text": 44}, "technical-implementation", "Technical Implementation", {"depth": 46, "slug": 47, "text": 48}, 3, "astro-framework", "Astro Framework", {"depth": 46, "slug": 50, "text": 51}, "decap-cms-integration", "Decap CMS Integration", {"depth": 46, "slug": 53, "text": 54}, "deployment", "Deployment", {"depth": 39, "slug": 56, "text": 57}, "development-process", "Development Process", [], [], {"title": 14, "description": 15, "image": 16, "technologies": 61, "liveUrl": 23, "githubUrl": 24, "featured": 25, "publishDate": 62, "status": 27}, [18, 19, 20, 21, 22], ["Date", "2024-06-05T10:00:00.000Z"], [], "portfolio-website.md", "task-management-app", {"id": 65, "data": 67, "body": 80, "filePath": 81, "digest": 82, "rendered": 83, "legacyId": 113}, {"title": 68, "description": 69, "image": 70, "technologies": 71, "liveUrl": 77, "githubUrl": 78, "featured": 25, "publishDate": 79, "status": 27}, "Task Management Application", "A full-stack task management application with real-time collaboration features and intuitive user interface.", "/images/task-app-preview.jpg", [72, 73, 74, 75, 76], "React", "Node.js", "MongoDB", "Socket.io", "Express", "https://taskmanager-demo.vercel.app", "https://github.com/bagusfarisa/task-manager", ["Date", "2024-05-15T10:00:00.000Z"], "# Task Management Application\n\nA comprehensive task management solution designed for teams and individuals to organize, track, and collaborate on projects efficiently.\n\n## Key Features\n\n- **Real-time Collaboration**: Multiple users can work on the same project simultaneously\n- **Drag & Drop Interface**: Intuitive task organization with drag-and-drop functionality\n- **Project Management**: Create and manage multiple projects with different team members\n- **Progress Tracking**: Visual progress indicators and completion statistics\n- **Notifications**: Real-time notifications for task updates and deadlines\n\n## Technical Stack\n\n### Frontend\n- **React**: Component-based UI development\n- **Redux Toolkit**: State management for complex application state\n- **React Beautiful DnD**: Smooth drag-and-drop interactions\n- **Styled Components**: CSS-in-JS for component styling\n- **React Router**: Client-side routing\n\n### Backend\n- **Node.js**: Server-side JavaScript runtime\n- **Express.js**: Web application framework\n- **MongoDB**: NoSQL database for flexible data storage\n- **Socket.io**: Real-time bidirectional communication\n- **JWT**: Secure authentication and authorization\n\n## Architecture\n\nThe application follows a modern full-stack architecture:\n\n1. **Client-Server Communication**: RESTful API with real-time WebSocket connections\n2. **Database Design**: Optimized MongoDB schemas for users, projects, and tasks\n3. **Authentication**: JWT-based authentication with refresh token rotation\n4. **Real-time Updates**: Socket.io for instant collaboration features\n\n## Challenges Solved\n\n- **Concurrent Editing**: Implemented conflict resolution for simultaneous task updates\n- **Performance**: Optimized database queries and implemented efficient caching\n- **User Experience**: Created intuitive interfaces for complex project management workflows\n- **Scalability**: Designed architecture to handle growing user base and data volume\n\nThis project showcases my full-stack development capabilities and understanding of modern web application architecture.", "src/content/projects/task-management-app.md", "e235bfba360edfbe", {"html": 84, "metadata": 85}, "<h1 id=\"task-management-application\">Task Management Application</h1>\n<p>A comprehensive task management solution designed for teams and individuals to organize, track, and collaborate on projects efficiently.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Real-time Collaboration</strong>: Multiple users can work on the same project simultaneously</li>\n<li><strong>Drag &#x26; Drop Interface</strong>: Intuitive task organization with drag-and-drop functionality</li>\n<li><strong>Project Management</strong>: Create and manage multiple projects with different team members</li>\n<li><strong>Progress Tracking</strong>: Visual progress indicators and completion statistics</li>\n<li><strong>Notifications</strong>: Real-time notifications for task updates and deadlines</li>\n</ul>\n<h2 id=\"technical-stack\">Technical Stack</h2>\n<h3 id=\"frontend\">Frontend</h3>\n<ul>\n<li><strong>React</strong>: Component-based UI development</li>\n<li><strong>Redux Toolkit</strong>: State management for complex application state</li>\n<li><strong>React Beautiful DnD</strong>: Smooth drag-and-drop interactions</li>\n<li><strong>Styled Components</strong>: CSS-in-JS for component styling</li>\n<li><strong>React Router</strong>: Client-side routing</li>\n</ul>\n<h3 id=\"backend\">Backend</h3>\n<ul>\n<li><strong>Node.js</strong>: Server-side JavaScript runtime</li>\n<li><strong>Express.js</strong>: Web application framework</li>\n<li><strong>MongoDB</strong>: NoSQL database for flexible data storage</li>\n<li><strong>Socket.io</strong>: Real-time bidirectional communication</li>\n<li><strong>JWT</strong>: Secure authentication and authorization</li>\n</ul>\n<h2 id=\"architecture\">Architecture</h2>\n<p>The application follows a modern full-stack architecture:</p>\n<ol>\n<li><strong>Client-Server Communication</strong>: RESTful API with real-time WebSocket connections</li>\n<li><strong>Database Design</strong>: Optimized MongoDB schemas for users, projects, and tasks</li>\n<li><strong>Authentication</strong>: JWT-based authentication with refresh token rotation</li>\n<li><strong>Real-time Updates</strong>: Socket.io for instant collaboration features</li>\n</ol>\n<h2 id=\"challenges-solved\">Challenges Solved</h2>\n<ul>\n<li><strong>Concurrent Editing</strong>: Implemented conflict resolution for simultaneous task updates</li>\n<li><strong>Performance</strong>: Optimized database queries and implemented efficient caching</li>\n<li><strong>User Experience</strong>: Created intuitive interfaces for complex project management workflows</li>\n<li><strong>Scalability</strong>: Designed architecture to handle growing user base and data volume</li>\n</ul>\n<p>This project showcases my full-stack development capabilities and understanding of modern web application architecture.</p>", {"headings": 86, "localImagePaths": 107, "remoteImagePaths": 108, "frontmatter": 109, "imagePaths": 112}, [87, 89, 92, 95, 98, 101, 104], {"depth": 36, "slug": 88, "text": 68}, "task-management-application", {"depth": 39, "slug": 90, "text": 91}, "key-features", "Key Features", {"depth": 39, "slug": 93, "text": 94}, "technical-stack", "Technical Stack", {"depth": 46, "slug": 96, "text": 97}, "frontend", "Frontend", {"depth": 46, "slug": 99, "text": 100}, "backend", "Backend", {"depth": 39, "slug": 102, "text": 103}, "architecture", "Architecture", {"depth": 39, "slug": 105, "text": 106}, "challenges-solved", "Challenges Solved", [], [], {"title": 68, "description": 69, "image": 70, "technologies": 110, "liveUrl": 77, "githubUrl": 78, "featured": 25, "publishDate": 111, "status": 27}, [72, 73, 74, 75, 76], ["Date", "2024-05-15T10:00:00.000Z"], [], "task-management-app.md", "blog", ["Map", 116, 117, 203, 204], "2024-05-28-modern-css-techniques", {"id": 116, "data": 118, "body": 129, "filePath": 130, "digest": 131, "rendered": 132, "legacyId": 202}, {"title": 119, "description": 120, "publishDate": 121, "author": 122, "image": 123, "tags": 124, "category": 127, "featured": 128, "draft": 128}, "Modern CSS Techniques Every Developer Should Know", "Explore the latest CSS features and techniques that can improve your web development workflow and create better user experiences.", ["Date", "2024-05-28T14:30:00.000Z"], "Gun<PERSON>", "/images/css-techniques-cover.jpg", [21, 125, 97, 126], "Web Development", "Design", "Tutorial", false, "# Modern CSS Techniques Every Developer Should Know\n\nCSS has evolved tremendously over the past few years, introducing powerful new features that make styling more intuitive and maintainable. Let's explore some modern CSS techniques that every developer should have in their toolkit.\n\n## CSS Custom Properties (Variables)\n\nCSS custom properties have revolutionized how we handle theming and maintainable styles:\n\n```css\n:root {\n  --primary-color: #2563eb;\n  --secondary-color: #64748b;\n  --border-radius: 8px;\n  --spacing-unit: 1rem;\n}\n\n.button {\n  background-color: var(--primary-color);\n  border-radius: var(--border-radius);\n  padding: calc(var(--spacing-unit) * 0.5) var(--spacing-unit);\n}\n\n/* Dynamic theming */\n[data-theme=\"dark\"] {\n  --primary-color: #3b82f6;\n  --secondary-color: #94a3b8;\n}\n```\n\n## Container Queries\n\nContainer queries allow components to respond to their container's size rather than the viewport:\n\n```css\n.card-container {\n  container-type: inline-size;\n}\n\n@container (min-width: 400px) {\n  .card {\n    display: grid;\n    grid-template-columns: 1fr 2fr;\n    gap: 1rem;\n  }\n}\n```\n\n## CSS Grid and Subgrid\n\nCSS Grid has matured with powerful features like subgrid:\n\n```css\n.layout {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 2rem;\n}\n\n.card {\n  display: grid;\n  grid-template-rows: subgrid;\n  grid-row: span 3;\n}\n```\n\n## Logical Properties\n\nLogical properties make internationalization easier:\n\n```css\n.content {\n  /* Instead of margin-left and margin-right */\n  margin-inline: 1rem;\n  \n  /* Instead of padding-top and padding-bottom */\n  padding-block: 2rem;\n  \n  /* Instead of border-left */\n  border-inline-start: 2px solid var(--primary-color);\n}\n```\n\n## Modern Layout Techniques\n\n### Intrinsic Web Design\n```css\n.responsive-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n```\n\n### Aspect Ratio\n```css\n.video-container {\n  aspect-ratio: 16 / 9;\n  background: #000;\n}\n\n.square-image {\n  aspect-ratio: 1;\n  object-fit: cover;\n}\n```\n\n## Advanced Selectors\n\n### :has() Pseudo-class\n```css\n/* Style a card differently if it contains an image */\n.card:has(img) {\n  grid-template-rows: auto 1fr auto;\n}\n\n/* Style a form when it has invalid inputs */\n.form:has(:invalid) {\n  border-color: red;\n}\n```\n\n### :where() and :is()\n```css\n/* Lower specificity with :where() */\n:where(h1, h2, h3) {\n  margin-block: 1em 0.5em;\n}\n\n/* Forgiving selector list with :is() */\n:is(.dark-theme, .high-contrast) .button {\n  border: 2px solid currentColor;\n}\n```\n\n## CSS Functions\n\n### clamp()\n```css\n.responsive-text {\n  font-size: clamp(1rem, 4vw, 2rem);\n}\n\n.flexible-width {\n  width: clamp(300px, 50%, 800px);\n}\n```\n\n### min() and max()\n```css\n.container {\n  width: min(100% - 2rem, 1200px);\n  margin-inline: auto;\n}\n```\n\n## Color Functions\n\n### color-mix()\n```css\n.button {\n  background: color-mix(in srgb, var(--primary-color) 80%, white);\n}\n\n.hover-state {\n  background: color-mix(in srgb, var(--primary-color), black 10%);\n}\n```\n\n## Performance Optimizations\n\n### content-visibility\n```css\n.long-content {\n  content-visibility: auto;\n  contain-intrinsic-size: 1000px;\n}\n```\n\n### will-change\n```css\n.animated-element {\n  will-change: transform;\n  transition: transform 0.3s ease;\n}\n\n.animated-element:hover {\n  transform: translateY(-4px);\n}\n```\n\n## Best Practices\n\n1. **Use Logical Properties**: Better for internationalization\n2. **Leverage Custom Properties**: Easier theming and maintenance\n3. **Embrace Container Queries**: More flexible responsive design\n4. **Optimize with Modern Functions**: Better performance and flexibility\n5. **Progressive Enhancement**: Use feature queries for newer features\n\n```css\n@supports (container-type: inline-size) {\n  /* Container query styles */\n}\n\n@supports not (aspect-ratio: 1) {\n  /* Fallback styles */\n}\n```\n\n## Conclusion\n\nModern CSS provides powerful tools for creating maintainable, performant, and flexible stylesheets. By embracing these techniques, you can write cleaner code, create better user experiences, and future-proof your projects.\n\nThe key is to gradually adopt these features while maintaining browser support for your target audience. Start with the most widely supported features and progressively enhance with newer capabilities.", "src/content/blog/2024-05-28-modern-css-techniques.md", "16ee6238eb8e7e31", {"html": 133, "metadata": 134}, "<h1 id=\"modern-css-techniques-every-developer-should-know\">Modern CSS Techniques Every Developer Should Know</h1>\n<p>CSS has evolved tremendously over the past few years, introducing powerful new features that make styling more intuitive and maintainable. Let’s explore some modern CSS techniques that every developer should have in their toolkit.</p>\n<h2 id=\"css-custom-properties-variables\">CSS Custom Properties (Variables)</h2>\n<p>CSS custom properties have revolutionized how we handle theming and maintainable styles:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">:root</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --primary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#2563eb</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --secondary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#64748b</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --border-radius</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">8</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --spacing-unit</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.button</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border-radius</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--border-radius</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  padding</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">calc</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--spacing-unit</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> 0.5</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--spacing-unit</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">/* Dynamic theming */</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">[</span><span style=\"color:#B392F0\">data-theme</span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\">\"dark\"</span><span style=\"color:#E1E4E8\">] {</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --primary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#3b82f6</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --secondary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#94a3b8</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"container-queries\">Container Queries</h2>\n<p>Container queries allow components to respond to their container’s size rather than the viewport:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.card-container</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  container-type</span><span style=\"color:#E1E4E8\">: inline-size;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">@container</span><span style=\"color:#E1E4E8\"> (min-width: 400px) {</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  .card</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    grid-template-columns</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#79B8FF\"> 2</span><span style=\"color:#F97583\">fr</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    gap</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"css-grid-and-subgrid\">CSS Grid and Subgrid</h2>\n<p>CSS Grid has matured with powerful features like subgrid:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.layout</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-columns</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">repeat</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">3</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  gap</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.card</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-rows</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">subgrid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-row</span><span style=\"color:#E1E4E8\">: span </span><span style=\"color:#79B8FF\">3</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"logical-properties\">Logical Properties</h2>\n<p>Logical properties make internationalization easier:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.content</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Instead of margin-left and margin-right */</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  margin-inline</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Instead of padding-top and padding-bottom */</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  padding-block</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Instead of border-left */</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border-inline-start</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">px</span><span style=\"color:#79B8FF\"> solid</span><span style=\"color:#79B8FF\"> var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"modern-layout-techniques\">Modern Layout Techniques</h2>\n<h3 id=\"intrinsic-web-design\">Intrinsic Web Design</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.responsive-grid</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-columns</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">repeat</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">auto-fit</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">minmax</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">300</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#E1E4E8\">));</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  gap</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"aspect-ratio\">Aspect Ratio</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.video-container</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  aspect-ratio</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">16</span><span style=\"color:#E1E4E8\"> / </span><span style=\"color:#79B8FF\">9</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#000</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.square-image</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  aspect-ratio</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  object-fit</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">cover</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"advanced-selectors\">Advanced Selectors</h2>\n<h3 id=\"has-pseudo-class\">:has() Pseudo-class</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#6A737D\">/* Style a card differently if it contains an image */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.card:has</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#85E89D\">img</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-rows</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">auto</span><span style=\"color:#79B8FF\"> 1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#79B8FF\"> auto</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">/* Style a form when it has invalid inputs */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.form:has</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#B392F0\">:invalid</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">red</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"where-and-is\">:where() and :is()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#6A737D\">/* Lower specificity with :where() */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">:where</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#85E89D\">h1</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#85E89D\">h2</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#85E89D\">h3</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  margin-block</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">em</span><span style=\"color:#79B8FF\"> 0.5</span><span style=\"color:#F97583\">em</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">/* Forgiving selector list with :is() */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">:is</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#B392F0\">.dark-theme</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#B392F0\">.high-contrast</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#B392F0\">.button</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">px</span><span style=\"color:#79B8FF\"> solid</span><span style=\"color:#79B8FF\"> currentColor</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"css-functions\">CSS Functions</h2>\n<h3 id=\"clamp\">clamp()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.responsive-text</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  font-size</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">clamp</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">4</span><span style=\"color:#F97583\">vw</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.flexible-width</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  width</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">clamp</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">300</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">50</span><span style=\"color:#F97583\">%</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">800</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"min-and-max\">min() and max()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.container</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  width</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">min</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">100</span><span style=\"color:#F97583\">%</span><span style=\"color:#FFAB70\"> -</span><span style=\"color:#79B8FF\"> 2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">1200</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  margin-inline</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">auto</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"color-functions\">Color Functions</h2>\n<h3 id=\"color-mix\">color-mix()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.button</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">color-mix</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">in</span><span style=\"color:#79B8FF\"> srgb</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#79B8FF\">80</span><span style=\"color:#F97583\">%</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">white</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.hover-state</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">color-mix</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">in</span><span style=\"color:#79B8FF\"> srgb</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">), </span><span style=\"color:#79B8FF\">black</span><span style=\"color:#79B8FF\"> 10</span><span style=\"color:#F97583\">%</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"performance-optimizations\">Performance Optimizations</h2>\n<h3 id=\"content-visibility\">content-visibility</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.long-content</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  content-visibility</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">auto</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  contain-intrinsic-size</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1000</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"will-change\">will-change</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.animated-element</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  will-change</span><span style=\"color:#E1E4E8\">: transform;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  transition</span><span style=\"color:#E1E4E8\">: transform </span><span style=\"color:#79B8FF\">0.3</span><span style=\"color:#F97583\">s</span><span style=\"color:#79B8FF\"> ease</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.animated-element:hover</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  transform</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">translateY</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">-4</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"best-practices\">Best Practices</h2>\n<ol>\n<li><strong>Use Logical Properties</strong>: Better for internationalization</li>\n<li><strong>Leverage Custom Properties</strong>: Easier theming and maintenance</li>\n<li><strong>Embrace Container Queries</strong>: More flexible responsive design</li>\n<li><strong>Optimize with Modern Functions</strong>: Better performance and flexibility</li>\n<li><strong>Progressive Enhancement</strong>: Use feature queries for newer features</li>\n</ol>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#F97583\">@supports</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">container-type</span><span style=\"color:#E1E4E8\">: inline-size) {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Container query styles */</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">@supports</span><span style=\"color:#F97583\"> not</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">aspect-ratio</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Fallback styles */</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"conclusion\">Conclusion</h2>\n<p>Modern CSS provides powerful tools for creating maintainable, performant, and flexible stylesheets. By embracing these techniques, you can write cleaner code, create better user experiences, and future-proof your projects.</p>\n<p>The key is to gradually adopt these features while maintaining browser support for your target audience. Start with the most widely supported features and progressively enhance with newer capabilities.</p>", {"headings": 135, "localImagePaths": 196, "remoteImagePaths": 197, "frontmatter": 198, "imagePaths": 201}, [136, 138, 141, 144, 147, 150, 153, 156, 159, 162, 165, 168, 171, 174, 177, 180, 183, 186, 188, 190, 193], {"depth": 36, "slug": 137, "text": 119}, "modern-css-techniques-every-developer-should-know", {"depth": 39, "slug": 139, "text": 140}, "css-custom-properties-variables", "CSS Custom Properties (Variables)", {"depth": 39, "slug": 142, "text": 143}, "container-queries", "Container Queries", {"depth": 39, "slug": 145, "text": 146}, "css-grid-and-subgrid", "CSS Grid and Subgrid", {"depth": 39, "slug": 148, "text": 149}, "logical-properties", "Logical Properties", {"depth": 39, "slug": 151, "text": 152}, "modern-layout-techniques", "Modern Layout Techniques", {"depth": 46, "slug": 154, "text": 155}, "intrinsic-web-design", "Intrinsic Web Design", {"depth": 46, "slug": 157, "text": 158}, "aspect-ratio", "Aspect Ratio", {"depth": 39, "slug": 160, "text": 161}, "advanced-selectors", "Advanced Selectors", {"depth": 46, "slug": 163, "text": 164}, "has-pseudo-class", ":has() Pseudo-class", {"depth": 46, "slug": 166, "text": 167}, "where-and-is", ":where() and :is()", {"depth": 39, "slug": 169, "text": 170}, "css-functions", "CSS Functions", {"depth": 46, "slug": 172, "text": 173}, "clamp", "clamp()", {"depth": 46, "slug": 175, "text": 176}, "min-and-max", "min() and max()", {"depth": 39, "slug": 178, "text": 179}, "color-functions", "Color Functions", {"depth": 46, "slug": 181, "text": 182}, "color-mix", "color-mix()", {"depth": 39, "slug": 184, "text": 185}, "performance-optimizations", "Performance Optimizations", {"depth": 46, "slug": 187, "text": 187}, "content-visibility", {"depth": 46, "slug": 189, "text": 189}, "will-change", {"depth": 39, "slug": 191, "text": 192}, "best-practices", "Best Practices", {"depth": 39, "slug": 194, "text": 195}, "conclusion", "Conclusion", [], [], {"title": 119, "description": 120, "publishDate": 199, "author": 122, "image": 123, "tags": 200, "category": 127, "featured": 128, "draft": 128}, ["Date", "2024-05-28T14:30:00.000Z"], [21, 125, 97, 126], [], "2024-05-28-modern-css-techniques.md", "2024-06-05-getting-started-with-astro", {"id": 203, "data": 205, "body": 213, "filePath": 214, "digest": 215, "rendered": 216, "legacyId": 265}, {"title": 206, "description": 207, "publishDate": 208, "author": 122, "image": 209, "tags": 210, "category": 127, "featured": 25, "draft": 128}, "Getting Started with Astro: A Modern Static Site Generator", "Learn how to build fast, content-focused websites with Astro framework and why it's becoming a popular choice for developers.", ["Date", "2024-06-05T09:00:00.000Z"], "/images/astro-blog-cover.jpg", [18, 125, 211, 212], "Static Sites", "JavaScript", "# Getting Started with Astro: A Modern Static Site Generator\n\nAstro has been gaining significant traction in the web development community, and for good reason. It's a modern static site generator that prioritizes performance and developer experience while offering unique features that set it apart from other frameworks.\n\n## What Makes Astro Special?\n\n### Zero JavaScript by Default\nOne of Astro's most compelling features is its \"zero JavaScript by default\" approach. Unlike other frameworks that ship JavaScript to the browser regardless of whether it's needed, Astro only includes JavaScript when you explicitly request it.\n\n```astro\n---\n// This runs on the server, not in the browser\nconst data = await fetch('https://api.example.com/data');\n---\n\n<div>\n  <!-- This is just HTML, no JavaScript needed -->\n  <h1>Welcome to my site</h1>\n  <p>Data loaded: {data.length} items</p>\n</div>\n```\n\n### Component Islands Architecture\nAstro introduces the concept of \"islands\" - interactive components that are hydrated independently. This means you can have a mostly static page with small interactive elements without shipping JavaScript for the entire page.\n\n```astro\n---\nimport InteractiveCounter from './InteractiveCounter.jsx';\nimport StaticHeader from './StaticHeader.astro';\n---\n\n<StaticHeader />\n<!-- This component will be hydrated -->\n<InteractiveCounter client:load />\n<!-- Rest of the page remains static -->\n<footer>Static footer content</footer>\n```\n\n## Key Benefits\n\n### Performance\n- **Faster Load Times**: Minimal JavaScript means faster initial page loads\n- **Better Core Web Vitals**: Optimized for Google's performance metrics\n- **Efficient Bundling**: Only ships the JavaScript you actually need\n\n### Developer Experience\n- **Framework Agnostic**: Use React, Vue, Svelte, or any framework you prefer\n- **TypeScript Support**: Built-in TypeScript support without configuration\n- **Hot Module Replacement**: Fast development with instant updates\n\n### SEO and Accessibility\n- **Server-Side Rendering**: Content is rendered on the server for better SEO\n- **Static HTML**: Search engines can easily crawl and index your content\n- **Progressive Enhancement**: Works even when JavaScript is disabled\n\n## Getting Started\n\n### Installation\n```bash\nnpm create astro@latest my-astro-site\ncd my-astro-site\nnpm install\nnpm run dev\n```\n\n### Project Structure\n```\nsrc/\n├── components/     # Reusable components\n├── layouts/        # Page layouts\n├── pages/          # File-based routing\n└── content/        # Content collections\n```\n\n### Content Collections\nAstro's content collections provide type-safe content management:\n\n```typescript\n// src/content/config.ts\nimport { defineCollection, z } from 'astro:content';\n\nconst blogCollection = defineCollection({\n  type: 'content',\n  schema: z.object({\n    title: z.string(),\n    publishDate: z.date(),\n    tags: z.array(z.string()),\n  }),\n});\n\nexport const collections = {\n  'blog': blogCollection,\n};\n```\n\n## When to Choose Astro\n\nAstro is particularly well-suited for:\n\n- **Content-focused sites**: Blogs, documentation, marketing sites\n- **E-commerce sites**: Product catalogs, landing pages\n- **Portfolio sites**: Showcasing work with minimal interactivity\n- **Corporate websites**: Company sites with occasional interactive elements\n\n## Conclusion\n\nAstro represents a thoughtful approach to modern web development, prioritizing performance without sacrificing developer experience. Its unique architecture makes it an excellent choice for content-focused websites that need to be fast, SEO-friendly, and maintainable.\n\nWhether you're building a personal blog, a company website, or a documentation site, Astro provides the tools and performance characteristics to create exceptional web experiences.\n\nReady to give Astro a try? Start with their excellent documentation and join the growing community of developers who are building faster websites with less JavaScript.", "src/content/blog/2024-06-05-getting-started-with-astro.md", "e1397bb316e21e84", {"html": 217, "metadata": 218}, "<h1 id=\"getting-started-with-astro-a-modern-static-site-generator\">Getting Started with Astro: A Modern Static Site Generator</h1>\n<p>Astro has been gaining significant traction in the web development community, and for good reason. It’s a modern static site generator that prioritizes performance and developer experience while offering unique features that set it apart from other frameworks.</p>\n<h2 id=\"what-makes-astro-special\">What Makes Astro Special?</h2>\n<h3 id=\"zero-javascript-by-default\">Zero JavaScript by Default</h3>\n<p>One of Astro’s most compelling features is its “zero JavaScript by default” approach. Unlike other frameworks that ship JavaScript to the browser regardless of whether it’s needed, Astro only includes JavaScript when you explicitly request it.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"astro\"><code><span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">// This runs on the server, not in the browser</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">const</span><span style=\"color:#79B8FF\"> data</span><span style=\"color:#F97583\"> =</span><span style=\"color:#F97583\"> await</span><span style=\"color:#B392F0\"> fetch</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#9ECBFF\">'https://api.example.com/data'</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#85E89D\">div</span><span style=\"color:#E1E4E8\">></span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  &#x3C;!-- This is just HTML, no JavaScript needed --></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  &#x3C;</span><span style=\"color:#85E89D\">h1</span><span style=\"color:#E1E4E8\">>Welcome to my site&#x3C;/</span><span style=\"color:#85E89D\">h1</span><span style=\"color:#E1E4E8\">></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  &#x3C;</span><span style=\"color:#85E89D\">p</span><span style=\"color:#E1E4E8\">>Data loaded: {data.length} items&#x3C;/</span><span style=\"color:#85E89D\">p</span><span style=\"color:#E1E4E8\">></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;/</span><span style=\"color:#85E89D\">div</span><span style=\"color:#E1E4E8\">></span></span></code></pre>\n<h3 id=\"component-islands-architecture\">Component Islands Architecture</h3>\n<p>Astro introduces the concept of “islands” - interactive components that are hydrated independently. This means you can have a mostly static page with small interactive elements without shipping JavaScript for the entire page.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"astro\"><code><span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">import</span><span style=\"color:#E1E4E8\"> InteractiveCounter </span><span style=\"color:#F97583\">from</span><span style=\"color:#9ECBFF\"> './InteractiveCounter.jsx'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">import</span><span style=\"color:#E1E4E8\"> StaticHeader </span><span style=\"color:#F97583\">from</span><span style=\"color:#9ECBFF\"> './StaticHeader.astro'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#79B8FF\">StaticHeader</span><span style=\"color:#E1E4E8\"> /></span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">&#x3C;!-- This component will be hydrated --></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#79B8FF\">InteractiveCounter</span><span style=\"color:#B392F0\"> client:load</span><span style=\"color:#E1E4E8\"> /></span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">&#x3C;!-- Rest of the page remains static --></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#85E89D\">footer</span><span style=\"color:#E1E4E8\">>Static footer content&#x3C;/</span><span style=\"color:#85E89D\">footer</span><span style=\"color:#E1E4E8\">></span></span></code></pre>\n<h2 id=\"key-benefits\">Key Benefits</h2>\n<h3 id=\"performance\">Performance</h3>\n<ul>\n<li><strong>Faster Load Times</strong>: Minimal JavaScript means faster initial page loads</li>\n<li><strong>Better Core Web Vitals</strong>: Optimized for Google’s performance metrics</li>\n<li><strong>Efficient Bundling</strong>: Only ships the JavaScript you actually need</li>\n</ul>\n<h3 id=\"developer-experience\">Developer Experience</h3>\n<ul>\n<li><strong>Framework Agnostic</strong>: Use React, Vue, Svelte, or any framework you prefer</li>\n<li><strong>TypeScript Support</strong>: Built-in TypeScript support without configuration</li>\n<li><strong>Hot Module Replacement</strong>: Fast development with instant updates</li>\n</ul>\n<h3 id=\"seo-and-accessibility\">SEO and Accessibility</h3>\n<ul>\n<li><strong>Server-Side Rendering</strong>: Content is rendered on the server for better SEO</li>\n<li><strong>Static HTML</strong>: Search engines can easily crawl and index your content</li>\n<li><strong>Progressive Enhancement</strong>: Works even when JavaScript is disabled</li>\n</ul>\n<h2 id=\"getting-started\">Getting Started</h2>\n<h3 id=\"installation\">Installation</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"bash\"><code><span class=\"line\"><span style=\"color:#B392F0\">npm</span><span style=\"color:#9ECBFF\"> create</span><span style=\"color:#9ECBFF\"> astro@latest</span><span style=\"color:#9ECBFF\"> my-astro-site</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">cd</span><span style=\"color:#9ECBFF\"> my-astro-site</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">npm</span><span style=\"color:#9ECBFF\"> install</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">npm</span><span style=\"color:#9ECBFF\"> run</span><span style=\"color:#9ECBFF\"> dev</span></span></code></pre>\n<h3 id=\"project-structure\">Project Structure</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"plaintext\"><code><span class=\"line\"><span>src/</span></span>\n<span class=\"line\"><span>├── components/     # Reusable components</span></span>\n<span class=\"line\"><span>├── layouts/        # Page layouts</span></span>\n<span class=\"line\"><span>├── pages/          # File-based routing</span></span>\n<span class=\"line\"><span>└── content/        # Content collections</span></span></code></pre>\n<h3 id=\"content-collections\">Content Collections</h3>\n<p>Astro’s content collections provide type-safe content management:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"typescript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// src/content/config.ts</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">import</span><span style=\"color:#E1E4E8\"> { defineCollection, z } </span><span style=\"color:#F97583\">from</span><span style=\"color:#9ECBFF\"> 'astro:content'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">const</span><span style=\"color:#79B8FF\"> blogCollection</span><span style=\"color:#F97583\"> =</span><span style=\"color:#B392F0\"> defineCollection</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  type: </span><span style=\"color:#9ECBFF\">'content'</span><span style=\"color:#E1E4E8\">,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  schema: z.</span><span style=\"color:#B392F0\">object</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    title: z.</span><span style=\"color:#B392F0\">string</span><span style=\"color:#E1E4E8\">(),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    publishDate: z.</span><span style=\"color:#B392F0\">date</span><span style=\"color:#E1E4E8\">(),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    tags: z.</span><span style=\"color:#B392F0\">array</span><span style=\"color:#E1E4E8\">(z.</span><span style=\"color:#B392F0\">string</span><span style=\"color:#E1E4E8\">()),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">});</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">export</span><span style=\"color:#F97583\"> const</span><span style=\"color:#79B8FF\"> collections</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#9ECBFF\">  'blog'</span><span style=\"color:#E1E4E8\">: blogCollection,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">};</span></span></code></pre>\n<h2 id=\"when-to-choose-astro\">When to Choose Astro</h2>\n<p>Astro is particularly well-suited for:</p>\n<ul>\n<li><strong>Content-focused sites</strong>: Blogs, documentation, marketing sites</li>\n<li><strong>E-commerce sites</strong>: Product catalogs, landing pages</li>\n<li><strong>Portfolio sites</strong>: Showcasing work with minimal interactivity</li>\n<li><strong>Corporate websites</strong>: Company sites with occasional interactive elements</li>\n</ul>\n<h2 id=\"conclusion\">Conclusion</h2>\n<p>Astro represents a thoughtful approach to modern web development, prioritizing performance without sacrificing developer experience. Its unique architecture makes it an excellent choice for content-focused websites that need to be fast, SEO-friendly, and maintainable.</p>\n<p>Whether you’re building a personal blog, a company website, or a documentation site, Astro provides the tools and performance characteristics to create exceptional web experiences.</p>\n<p>Ready to give Astro a try? Start with their excellent documentation and join the growing community of developers who are building faster websites with less JavaScript.</p>", {"headings": 219, "localImagePaths": 259, "remoteImagePaths": 260, "frontmatter": 261, "imagePaths": 264}, [220, 222, 225, 228, 231, 234, 237, 240, 243, 246, 249, 252, 255, 258], {"depth": 36, "slug": 221, "text": 206}, "getting-started-with-astro-a-modern-static-site-generator", {"depth": 39, "slug": 223, "text": 224}, "what-makes-astro-special", "What Makes Astro Special?", {"depth": 46, "slug": 226, "text": 227}, "zero-javascript-by-default", "Zero JavaScript by <PERSON><PERSON><PERSON>", {"depth": 46, "slug": 229, "text": 230}, "component-islands-architecture", "Component Islands Architecture", {"depth": 39, "slug": 232, "text": 233}, "key-benefits", "Key Benefits", {"depth": 46, "slug": 235, "text": 236}, "performance", "Performance", {"depth": 46, "slug": 238, "text": 239}, "developer-experience", "Developer Experience", {"depth": 46, "slug": 241, "text": 242}, "seo-and-accessibility", "SEO and Accessibility", {"depth": 39, "slug": 244, "text": 245}, "getting-started", "Getting Started", {"depth": 46, "slug": 247, "text": 248}, "installation", "Installation", {"depth": 46, "slug": 250, "text": 251}, "project-structure", "Project Structure", {"depth": 46, "slug": 253, "text": 254}, "content-collections", "Content Collections", {"depth": 39, "slug": 256, "text": 257}, "when-to-choose-astro", "When to <PERSON><PERSON>", {"depth": 39, "slug": 194, "text": 195}, [], [], {"title": 206, "description": 207, "publishDate": 262, "author": 122, "image": 209, "tags": 263, "category": 127, "featured": 25, "draft": 128}, ["Date", "2024-06-05T09:00:00.000Z"], [18, 125, 211, 212], [], "2024-06-05-getting-started-with-astro.md"]