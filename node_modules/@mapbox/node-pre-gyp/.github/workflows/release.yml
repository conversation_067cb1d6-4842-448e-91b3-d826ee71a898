name: release

on:
  push:
    branches: [master]
  workflow_dispatch:

jobs:
  release-check:
    name: Check if version is published
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Check if version is published
        id: check
        run: |
          currentVersion="$( node -e "console.log(require('./package.json').version)" )"
          isPublished="$( npm view @mapbox/node-pre-gyp versions --json | jq -c --arg cv "$currentVersion" 'any(. == $cv)' )"
          echo "version=$currentVersion" >> "$GITHUB_OUTPUT"
          echo "published=$isPublished" >> "$GITHUB_OUTPUT"
          echo "currentVersion: $currentVersion"
          echo "isPublished: $isPublished"
    outputs:
      published: ${{ steps.check.outputs.published }}
      version: ${{ steps.check.outputs.version }}

  publish:
    needs: release-check
    if: ${{ needs.release-check.outputs.published == 'false' }}
    runs-on: ubuntu-latest
    permissions:
      contents: write
    defaults:
      run:
        shell: bash

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 22

      - run: npm ci

      - run: npm audit

      - run: npm run lint

      - run: npm run update-crosswalk  # To support newer versions of Node.js

      - run: npm run build --if-present
      
      - run: npm test

      - name: Prepare release changelog
        id: prepare_release
        run: |
          RELEASE_TYPE="$(node -e "console.log(require('semver').prerelease('${{ needs.release-check.outputs.version }}') ? 'prerelease' : 'regular')")"
          if [[ $RELEASE_TYPE == 'regular' ]]; then
            echo "prerelease=false" >> "$GITHUB_OUTPUT"
          else
            echo "prerelease=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Extract changelog for version
        run: |
          awk '/^##/ { p = 0 }; p == 1 { print }; $0 == "## ${{ needs.release-check.outputs.version }}" { p = 1 };' CHANGELOG.md > changelog_for_version.md
          cat changelog_for_version.md

      - name: Publish to Github
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag: v${{ needs.release-check.outputs.version }}
          name: v${{ needs.release-check.outputs.version }}
          bodyFile: changelog_for_version.md
          allowUpdates: true
          draft: false
          prerelease: ${{ steps.prepare_release.outputs.prerelease }}

      - name: Publish to NPM (release)
        if: ${{ steps.prepare_release.outputs.prerelease == 'false' }}
        run: |
          npm config set //registry.npmjs.org/:_authToken "${NPM_TOKEN}"
          npm publish --access public
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Publish to NPM (prerelease)
        if: ${{ steps.prepare_release.outputs.prerelease == 'true' }}
        run: |
          npm config set //registry.npmjs.org/:_authToken "${NPM_TOKEN}"
          npm publish --tag next --access public
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
