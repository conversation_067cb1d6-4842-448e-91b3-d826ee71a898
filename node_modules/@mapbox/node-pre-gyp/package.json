{"name": "@mapbox/node-pre-gyp", "description": "Node.js native addon binary install tool", "version": "2.0.0", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/mapbox/node-pre-gyp.git"}, "bin": "./bin/node-pre-gyp", "main": "./lib/node-pre-gyp.js", "engines": {"node": ">=18"}, "dependencies": {"consola": "^3.2.3", "detect-libc": "^2.0.0", "https-proxy-agent": "^7.0.5", "node-fetch": "^2.6.7", "nopt": "^8.0.0", "semver": "^7.5.3", "tar": "^7.4.0"}, "devDependencies": {"@mapbox/cloudfriend": "^8.1.0", "@mapbox/eslint-config-mapbox": "^5.0.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "eslint": "^8.57.0", "eslint-plugin-n": "^17.9.0", "mock-aws-s3": "^4.0.2", "nock": "^13.5.4", "node-addon-api": "^8.1.0", "nyc": "^17.0.0", "tape": "^5.5.2", "tar-fs": "^3.0.6"}, "nyc": {"all": true, "skip-full": false, "exclude": ["test/**"]}, "scripts": {"coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "fix": "npm run lint -- --fix", "update-crosswalk": "node scripts/abi_crosswalk.js", "test": "tape test/*test.js"}}