/* eslint-disable no-var */

declare global {
  // must be `var` to work
  var process: {
    env: Record<string, string>;
  };
}

/**
 * An extension to the standard `Request` object that is passed to every Edge Function.
 *
 * @example
 * ```ts
 * import type { RequestContext } from '@vercel/edge';
 *
 * export default async function handler(request: Request, ctx: RequestContext): Promise<Response> {
 *   // ctx is the RequestContext
 * }
 * ```
 */
interface RequestContext {
    /**
     * A method that can be used to keep the function running after a response has been sent.
     * This is useful when you have an async task that you want to keep running even after the
     * response has been sent and the request has ended.
     *
     * @example
     *
     * <caption>Sending an internal error to an error tracking service</caption>
     *
     * ```ts
     * import type { RequestContext } from '@vercel/edge';
     *
     * export async function handleRequest(request: Request, ctx: RequestContext): Promise<Response> {
     *  try {
     *    return await myFunctionThatReturnsResponse();
     *  } catch (e) {
     *    ctx.waitUntil((async () => {
     *      // report this error to your error tracking service
     *      await fetch('https://my-error-tracking-service.com', {
     *        method: 'POST',
     *        body: JSON.stringify({
     *          stack: e.stack,
     *          message: e.message,
     *          name: e.name,
     *          url: request.url,
     *        }),
     *      });
     *    })());
     *    return new Response('Internal Server Error', { status: 500 });
     *  }
     * }
     * ```
     */
    waitUntil(
    /**
     * A promise that will be kept alive until it resolves or rejects.
     */ promise: Promise<unknown>): void;
}

/**
 * City of the original client IP as calculated by Vercel Proxy.
 */
declare const CITY_HEADER_NAME = "x-vercel-ip-city";
/**
 * Country of the original client IP as calculated by Vercel Proxy.
 */
declare const COUNTRY_HEADER_NAME = "x-vercel-ip-country";
/**
 * Client IP as calculated by Vercel Proxy.
 */
declare const IP_HEADER_NAME = "x-real-ip";
/**
 * Latitude of the original client IP as calculated by Vercel Proxy.
 */
declare const LATITUDE_HEADER_NAME = "x-vercel-ip-latitude";
/**
 * Longitude of the original client IP as calculated by Vercel Proxy.
 */
declare const LONGITUDE_HEADER_NAME = "x-vercel-ip-longitude";
/**
 * Country region of the original client IP calculated by Vercel Proxy.
 *
 * See [docs](https://vercel.com/docs/concepts/edge-network/headers#x-vercel-ip-country-region).
 */
declare const REGION_HEADER_NAME = "x-vercel-ip-country-region";
/**
 * Postal code of the original client IP as calculated by Vercel Proxy.
 */
declare const POSTAL_CODE_HEADER_NAME = "x-vercel-ip-postal-code";
/**
 * The request ID for each request generated by Vercel Proxy.
 */
declare const REQUEST_ID_HEADER_NAME = "x-vercel-id";
/**
 * Unicode characters for emoji flags start at this number, and run up to 127469.
 */
declare const EMOJI_FLAG_UNICODE_STARTING_POSITION = 127397;
/**
 * Represents the headers of a request.
 */
interface Headers$1 {
    get(name: string): string | null;
}
/**
 * We define a new type so this function can be reused with the global `Request`, `node-fetch` and other types.
 */
interface Request {
    /**
     * Represents the headers of a request.
     */
    headers: Headers$1;
}
/**
 * The location information of a given request.
 */
interface Geo {
    /** The city that the request originated from. */
    city?: string;
    /** The country that the request originated from. */
    country?: string;
    /** The flag emoji for the country the request originated from. */
    flag?: string;
    /** The [Vercel Edge Network region](https://vercel.com/docs/concepts/edge-network/regions) that received the request. */
    region?: string;
    /** The region part of the ISO 3166-2 code of the client IP.
     * See [docs](https://vercel.com/docs/concepts/edge-network/headers#x-vercel-ip-country-region).
     */
    countryRegion?: string;
    /** The latitude of the client. */
    latitude?: string;
    /** The longitude of the client. */
    longitude?: string;
    /** The postal code of the client */
    postalCode?: string;
}
/**
 * Returns the IP address of the request from the headers.
 *
 * @param {(Request|Headers)} input The incoming request object or headers.
 * @returns The IP address of the request.
 *
 * @example
 *
 * ```js
 * import { ipAddress } from '@vercel/functions';
 *
 * export function GET(request) {
 *   const ip = ipAddress(request);
 *   return new Response(`Your IP is ${ip}`);
 * }
 * ```
 *
 */
declare function ipAddress(input: Request | Headers$1): string | undefined;
/**
 * Returns the location information for the incoming request.
 * @param request The incoming request object which provides the geolocation data
 * @returns The location information of the request, in this way:
 *
 * ```json
 * {
 *  "city": "New York",
 *  "country": "US",
 *  "flag": "🇺🇸",
 *  "countryRegion": "NY",
 *  "region": "iad1",
 *  "latitude": "40.7128",
 *  "longitude": "-74.0060"
 *  "postalCode": "10001"
 * }
 * ```
 *
 * @example
 *
 * ```js
 * import { geolocation } from '@vercel/functions';
 *
 * export function GET(request) {
 *   const details = geolocation(request);
 *   return Response.json(details);
 * }
 * ```
 */
declare function geolocation(request: Request): Geo;

interface ModifiedRequest {
    /**
     * If set, overwrites the incoming headers to the origin request.
     *
     * This is useful when you want to pass data between a Middleware and a
     * Serverless or Edge Function.
     *
     * @example
     * <caption>Add a `x-user-id` header and remove the `Authorization` header</caption>
     *
     * ```ts
     * import { rewrite } from '@vercel/edge';
     * export default async function middleware(request: Request): Promise<Response> {
     *   const newHeaders = new Headers(request.headers);
     *   newHeaders.set('x-user-id', 'user_123');
     *   newHeaders.delete('authorization');
     *   return rewrite(request.url, {
     *     request: { headers: newHeaders }
     *   })
     * }
     * ```
     */
    headers?: Headers;
}
interface ExtraResponseInit extends Omit<ResponseInit, 'headers'> {
    /**
     * These headers will be sent to the user response
     * along with the response headers from the origin.
     */
    headers?: HeadersInit;
    /**
     * Fields to rewrite for the upstream request.
     */
    request?: ModifiedRequest;
}
/**
 * Returns a response that rewrites the request to a different URL.
 *
 * @param destination new URL to rewrite the request to
 * @param init Additional options for the response
 *
 *
 * @example
 * <caption>Rewrite all feature-flagged requests from `/:path*` to `/experimental/:path*`</caption>
 *
 * ```ts
 * import { rewrite, next } from '@vercel/edge';
 *
 * export default async function middleware(req: Request) {
 *   const flagged = await getFlag(req, 'isExperimental');
 *   if (flagged) {
 *     const url = new URL(req.url);
 *     url.pathname = `/experimental{url.pathname}`;
 *     return rewrite(url);
 *   }
 *
 *   return next();
 * }
 * ```
 *
 * @example
 * <caption>JWT authentication for `/api/:path*` requests</caption>
 *
 * ```ts
 * import { rewrite, next } from '@vercel/edge';
 *
 * export default function middleware(req: Request) {
 *   const auth = checkJwt(req.headers.get('Authorization'));
 *   if (!checkJwt) {
 *     return rewrite(new URL('/api/error-unauthorized', req.url));
 *   }
 *   const url = new URL(req.url);
 *   url.searchParams.set('_userId', auth.userId);
 *   return rewrite(url);
 * }
 *
 * export const config = { matcher: '/api/users/:path*' };
 * ```
 */
declare function rewrite(destination: string | URL, init?: ExtraResponseInit): Response;
/**
 * Returns a Response that instructs the system to continue processing the request.
 *
 * @param init Additional options for the response
 *
 * @example
 * <caption>No-op middleware</caption>
 *
 * ```ts
 * import { next } from '@vercel/edge';
 *
 * export default function middleware(_req: Request) {
 *   return next();
 * }
 * ```
 *
 * @example
 * <caption>Add response headers to all requests</caption>
 *
 * ```ts
 * import { next } from '@vercel/edge';
 *
 * export default function middleware(_req: Request) {
 *   return next({
 *     headers: { 'x-from-middleware': 'true' },
 *   })
 * }
 * ```
 */
declare function next(init?: ExtraResponseInit): Response;

export { CITY_HEADER_NAME, COUNTRY_HEADER_NAME, EMOJI_FLAG_UNICODE_STARTING_POSITION, ExtraResponseInit, Geo, Headers$1 as Headers, IP_HEADER_NAME, LATITUDE_HEADER_NAME, LONGITUDE_HEADER_NAME, ModifiedRequest, POSTAL_CODE_HEADER_NAME, REGION_HEADER_NAME, REQUEST_ID_HEADER_NAME, Request, RequestContext, geolocation, ipAddress, next, rewrite };
