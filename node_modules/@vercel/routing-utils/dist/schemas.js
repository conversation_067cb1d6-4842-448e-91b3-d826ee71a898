"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var schemas_exports = {};
__export(schemas_exports, {
  cleanUrlsSchema: () => cleanUrlsSchema,
  hasSchema: () => hasSchema,
  headersSchema: () => headersSchema,
  redirectsSchema: () => redirectsSchema,
  rewritesSchema: () => rewritesSchema,
  routesSchema: () => routesSchema,
  trailingSlashSchema: () => trailingSlashSchema
});
module.exports = __toCommonJS(schemas_exports);
const hasSchema = {
  description: "An array of requirements that are needed to match",
  type: "array",
  maxItems: 16,
  items: {
    anyOf: [
      {
        type: "object",
        additionalProperties: false,
        required: ["type", "value"],
        properties: {
          type: {
            description: "The type of request element to check",
            type: "string",
            enum: ["host"]
          },
          value: {
            description: "A regular expression used to match the value. Named groups can be used in the destination",
            type: "string",
            maxLength: 4096
          }
        }
      },
      {
        type: "object",
        additionalProperties: false,
        required: ["type", "key"],
        properties: {
          type: {
            description: "The type of request element to check",
            type: "string",
            enum: ["header", "cookie", "query"]
          },
          key: {
            description: "The name of the element contained in the particular type",
            type: "string",
            maxLength: 4096
          },
          value: {
            description: "A regular expression used to match the value. Named groups can be used in the destination",
            type: "string",
            maxLength: 4096
          }
        }
      }
    ]
  }
};
const routesSchema = {
  type: "array",
  maxItems: 2048,
  deprecated: true,
  description: "A list of routes objects used to rewrite paths to point towards other internal or external paths",
  example: [{ dest: "https://docs.example.com", src: "/docs" }],
  items: {
    anyOf: [
      {
        type: "object",
        required: ["src"],
        additionalProperties: false,
        properties: {
          src: {
            type: "string",
            maxLength: 4096
          },
          dest: {
            type: "string",
            maxLength: 4096
          },
          headers: {
            type: "object",
            additionalProperties: false,
            minProperties: 1,
            maxProperties: 100,
            patternProperties: {
              "^.{1,256}$": {
                type: "string",
                maxLength: 4096
              }
            }
          },
          methods: {
            type: "array",
            maxItems: 10,
            items: {
              type: "string",
              maxLength: 32
            }
          },
          caseSensitive: {
            type: "boolean"
          },
          important: {
            type: "boolean"
          },
          user: {
            type: "boolean"
          },
          continue: {
            type: "boolean"
          },
          override: {
            type: "boolean"
          },
          check: {
            type: "boolean"
          },
          isInternal: {
            type: "boolean"
          },
          status: {
            type: "integer",
            minimum: 100,
            maximum: 999
          },
          locale: {
            type: "object",
            additionalProperties: false,
            minProperties: 1,
            properties: {
              redirect: {
                type: "object",
                additionalProperties: false,
                minProperties: 1,
                maxProperties: 100,
                patternProperties: {
                  "^.{1,256}$": {
                    type: "string",
                    maxLength: 4096
                  }
                }
              },
              value: {
                type: "string",
                maxLength: 4096
              },
              path: {
                type: "string",
                maxLength: 4096
              },
              cookie: {
                type: "string",
                maxLength: 4096
              },
              default: {
                type: "string",
                maxLength: 4096
              }
            }
          },
          middleware: { type: "number" },
          middlewarePath: { type: "string" },
          middlewareRawSrc: {
            type: "array",
            items: {
              type: "string"
            }
          },
          has: hasSchema,
          missing: hasSchema
        }
      },
      {
        type: "object",
        required: ["handle"],
        additionalProperties: false,
        properties: {
          handle: {
            type: "string",
            maxLength: 32,
            enum: ["error", "filesystem", "hit", "miss", "resource", "rewrite"]
          }
        }
      }
    ]
  }
};
const rewritesSchema = {
  type: "array",
  maxItems: 2048,
  description: "A list of rewrite definitions.",
  items: {
    type: "object",
    additionalProperties: false,
    required: ["source", "destination"],
    properties: {
      source: {
        description: "A pattern that matches each incoming pathname (excluding querystring).",
        type: "string",
        maxLength: 4096
      },
      destination: {
        description: "An absolute pathname to an existing resource or an external URL.",
        type: "string",
        maxLength: 4096
      },
      has: hasSchema,
      missing: hasSchema,
      statusCode: {
        description: "An optional integer to override the status code of the response.",
        type: "integer",
        minimum: 100,
        maximum: 999
      }
    }
  }
};
const redirectsSchema = {
  title: "Redirects",
  type: "array",
  maxItems: 2048,
  description: "A list of redirect definitions.",
  items: {
    type: "object",
    additionalProperties: false,
    required: ["source", "destination"],
    properties: {
      source: {
        description: "A pattern that matches each incoming pathname (excluding querystring).",
        type: "string",
        maxLength: 4096
      },
      destination: {
        description: "A location destination defined as an absolute pathname or external URL.",
        type: "string",
        maxLength: 4096
      },
      permanent: {
        description: "A boolean to toggle between permanent and temporary redirect. When `true`, the status code is `308`. When `false` the status code is `307`.",
        type: "boolean"
      },
      statusCode: {
        description: "An optional integer to define the status code of the redirect.",
        private: true,
        type: "integer",
        minimum: 100,
        maximum: 999
      },
      has: hasSchema,
      missing: hasSchema
    }
  }
};
const headersSchema = {
  type: "array",
  maxItems: 2048,
  description: "A list of header definitions.",
  items: {
    type: "object",
    additionalProperties: false,
    required: ["source", "headers"],
    properties: {
      source: {
        description: "A pattern that matches each incoming pathname (excluding querystring)",
        type: "string",
        maxLength: 4096
      },
      headers: {
        description: "An array of key/value pairs representing each response header.",
        type: "array",
        maxItems: 1024,
        items: {
          type: "object",
          additionalProperties: false,
          required: ["key", "value"],
          properties: {
            key: {
              type: "string",
              maxLength: 4096
            },
            value: {
              type: "string",
              maxLength: 4096
            }
          }
        }
      },
      has: hasSchema,
      missing: hasSchema
    }
  }
};
const cleanUrlsSchema = {
  description: "When set to `true`, all HTML files and Serverless Functions will have their extension removed. When visiting a path that ends with the extension, a 308 response will redirect the client to the extensionless path.",
  type: "boolean"
};
const trailingSlashSchema = {
  description: "When `false`, visiting a path that ends with a forward slash will respond with a `308` status code and redirect to the path without the trailing slash.",
  type: "boolean"
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  cleanUrlsSchema,
  hasSchema,
  headersSchema,
  redirectsSchema,
  rewritesSchema,
  routesSchema,
  trailingSlashSchema
});
