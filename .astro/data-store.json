[["Map", 1, 2, 9, 10, 168, 169], "meta::meta", ["Map", 3, 4, 5, 6, 7, 8], "astro-version", "5.8.2", "content-config-digest", "ffbf591254b1f6e4", "astro-config-digest", "{\"root\":{},\"srcDir\":{},\"publicDir\":{},\"outDir\":{},\"cacheDir\":{},\"site\":\"https://bagusfarisa-astro.vercel.app\",\"compressHTML\":true,\"base\":\"/\",\"trailingSlash\":\"ignore\",\"output\":\"server\",\"scopedStyleStrategy\":\"attribute\",\"build\":{\"format\":\"directory\",\"client\":{},\"server\":{},\"assets\":\"_astro\",\"serverEntry\":\"entry.mjs\",\"redirects\":false,\"inlineStylesheets\":\"auto\",\"concurrency\":1},\"server\":{\"open\":false,\"host\":false,\"port\":4321,\"streaming\":true,\"allowedHosts\":[]},\"redirects\":{},\"image\":{\"endpoint\":{\"route\":\"/_image\"},\"service\":{\"entrypoint\":\"astro/assets/services/sharp\",\"config\":{}},\"domains\":[],\"remotePatterns\":[],\"experimentalDefaultStyles\":true},\"devToolbar\":{\"enabled\":true},\"markdown\":{\"syntaxHighlight\":{\"type\":\"shiki\",\"excludeLangs\":[\"math\"]},\"shikiConfig\":{\"langs\":[],\"langAlias\":{},\"theme\":\"github-dark\",\"themes\":{},\"wrap\":false,\"transformers\":[]},\"remarkPlugins\":[],\"rehypePlugins\":[],\"remarkRehype\":{},\"gfm\":true,\"smartypants\":true},\"security\":{\"checkOrigin\":true},\"env\":{\"schema\":{},\"validateSecrets\":false},\"experimental\":{\"clientPrerender\":false,\"contentIntellisense\":false,\"responsiveImages\":false,\"headingIdCompat\":false,\"preserveScriptOrder\":false},\"legacy\":{\"collections\":false}}", "blog", ["Map", 11, 12, 85, 86], "2024-06-05-getting-started-with-astro", {"id": 11, "data": 13, "body": 27, "filePath": 28, "digest": 29, "rendered": 30, "legacyId": 84}, {"title": 14, "description": 15, "publishDate": 16, "author": 17, "image": 18, "tags": 19, "category": 24, "featured": 25, "draft": 26}, "Getting Started with Astro: A Modern Static Site Generator", "Learn how to build fast, content-focused websites with Astro framework and why it's becoming a popular choice for developers.", ["Date", "2024-06-05T09:00:00.000Z"], "Gun<PERSON>", "/images/astro-blog-cover.jpg", [20, 21, 22, 23], "Astro", "Web Development", "Static Sites", "JavaScript", "Tutorial", true, false, "# Getting Started with Astro: A Modern Static Site Generator\n\nAstro has been gaining significant traction in the web development community, and for good reason. It's a modern static site generator that prioritizes performance and developer experience while offering unique features that set it apart from other frameworks.\n\n## What Makes Astro Special?\n\n### Zero JavaScript by Default\nOne of Astro's most compelling features is its \"zero JavaScript by default\" approach. Unlike other frameworks that ship JavaScript to the browser regardless of whether it's needed, Astro only includes JavaScript when you explicitly request it.\n\n```astro\n---\n// This runs on the server, not in the browser\nconst data = await fetch('https://api.example.com/data');\n---\n\n<div>\n  <!-- This is just HTML, no JavaScript needed -->\n  <h1>Welcome to my site</h1>\n  <p>Data loaded: {data.length} items</p>\n</div>\n```\n\n### Component Islands Architecture\nAstro introduces the concept of \"islands\" - interactive components that are hydrated independently. This means you can have a mostly static page with small interactive elements without shipping JavaScript for the entire page.\n\n```astro\n---\nimport InteractiveCounter from './InteractiveCounter.jsx';\nimport StaticHeader from './StaticHeader.astro';\n---\n\n<StaticHeader />\n<!-- This component will be hydrated -->\n<InteractiveCounter client:load />\n<!-- Rest of the page remains static -->\n<footer>Static footer content</footer>\n```\n\n## Key Benefits\n\n### Performance\n- **Faster Load Times**: Minimal JavaScript means faster initial page loads\n- **Better Core Web Vitals**: Optimized for Google's performance metrics\n- **Efficient Bundling**: Only ships the JavaScript you actually need\n\n### Developer Experience\n- **Framework Agnostic**: Use React, Vue, Svelte, or any framework you prefer\n- **TypeScript Support**: Built-in TypeScript support without configuration\n- **Hot Module Replacement**: Fast development with instant updates\n\n### SEO and Accessibility\n- **Server-Side Rendering**: Content is rendered on the server for better SEO\n- **Static HTML**: Search engines can easily crawl and index your content\n- **Progressive Enhancement**: Works even when JavaScript is disabled\n\n## Getting Started\n\n### Installation\n```bash\nnpm create astro@latest my-astro-site\ncd my-astro-site\nnpm install\nnpm run dev\n```\n\n### Project Structure\n```\nsrc/\n├── components/     # Reusable components\n├── layouts/        # Page layouts\n├── pages/          # File-based routing\n└── content/        # Content collections\n```\n\n### Content Collections\nAstro's content collections provide type-safe content management:\n\n```typescript\n// src/content/config.ts\nimport { defineCollection, z } from 'astro:content';\n\nconst blogCollection = defineCollection({\n  type: 'content',\n  schema: z.object({\n    title: z.string(),\n    publishDate: z.date(),\n    tags: z.array(z.string()),\n  }),\n});\n\nexport const collections = {\n  'blog': blogCollection,\n};\n```\n\n## When to Choose Astro\n\nAstro is particularly well-suited for:\n\n- **Content-focused sites**: Blogs, documentation, marketing sites\n- **E-commerce sites**: Product catalogs, landing pages\n- **Portfolio sites**: Showcasing work with minimal interactivity\n- **Corporate websites**: Company sites with occasional interactive elements\n\n## Conclusion\n\nAstro represents a thoughtful approach to modern web development, prioritizing performance without sacrificing developer experience. Its unique architecture makes it an excellent choice for content-focused websites that need to be fast, SEO-friendly, and maintainable.\n\nWhether you're building a personal blog, a company website, or a documentation site, Astro provides the tools and performance characteristics to create exceptional web experiences.\n\nReady to give Astro a try? Start with their excellent documentation and join the growing community of developers who are building faster websites with less JavaScript.", "src/content/blog/2024-06-05-getting-started-with-astro.md", "e1397bb316e21e84", {"html": 31, "metadata": 32}, "<h1 id=\"getting-started-with-astro-a-modern-static-site-generator\">Getting Started with Astro: A Modern Static Site Generator</h1>\n<p>Astro has been gaining significant traction in the web development community, and for good reason. It’s a modern static site generator that prioritizes performance and developer experience while offering unique features that set it apart from other frameworks.</p>\n<h2 id=\"what-makes-astro-special\">What Makes Astro Special?</h2>\n<h3 id=\"zero-javascript-by-default\">Zero JavaScript by Default</h3>\n<p>One of Astro’s most compelling features is its “zero JavaScript by default” approach. Unlike other frameworks that ship JavaScript to the browser regardless of whether it’s needed, Astro only includes JavaScript when you explicitly request it.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"astro\"><code><span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">// This runs on the server, not in the browser</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">const</span><span style=\"color:#79B8FF\"> data</span><span style=\"color:#F97583\"> =</span><span style=\"color:#F97583\"> await</span><span style=\"color:#B392F0\"> fetch</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#9ECBFF\">'https://api.example.com/data'</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#85E89D\">div</span><span style=\"color:#E1E4E8\">></span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  &#x3C;!-- This is just HTML, no JavaScript needed --></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  &#x3C;</span><span style=\"color:#85E89D\">h1</span><span style=\"color:#E1E4E8\">>Welcome to my site&#x3C;/</span><span style=\"color:#85E89D\">h1</span><span style=\"color:#E1E4E8\">></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  &#x3C;</span><span style=\"color:#85E89D\">p</span><span style=\"color:#E1E4E8\">>Data loaded: {data.length} items&#x3C;/</span><span style=\"color:#85E89D\">p</span><span style=\"color:#E1E4E8\">></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;/</span><span style=\"color:#85E89D\">div</span><span style=\"color:#E1E4E8\">></span></span></code></pre>\n<h3 id=\"component-islands-architecture\">Component Islands Architecture</h3>\n<p>Astro introduces the concept of “islands” - interactive components that are hydrated independently. This means you can have a mostly static page with small interactive elements without shipping JavaScript for the entire page.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"astro\"><code><span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">import</span><span style=\"color:#E1E4E8\"> InteractiveCounter </span><span style=\"color:#F97583\">from</span><span style=\"color:#9ECBFF\"> './InteractiveCounter.jsx'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">import</span><span style=\"color:#E1E4E8\"> StaticHeader </span><span style=\"color:#F97583\">from</span><span style=\"color:#9ECBFF\"> './StaticHeader.astro'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">---</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#79B8FF\">StaticHeader</span><span style=\"color:#E1E4E8\"> /></span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">&#x3C;!-- This component will be hydrated --></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#79B8FF\">InteractiveCounter</span><span style=\"color:#B392F0\"> client:load</span><span style=\"color:#E1E4E8\"> /></span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">&#x3C;!-- Rest of the page remains static --></span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">&#x3C;</span><span style=\"color:#85E89D\">footer</span><span style=\"color:#E1E4E8\">>Static footer content&#x3C;/</span><span style=\"color:#85E89D\">footer</span><span style=\"color:#E1E4E8\">></span></span></code></pre>\n<h2 id=\"key-benefits\">Key Benefits</h2>\n<h3 id=\"performance\">Performance</h3>\n<ul>\n<li><strong>Faster Load Times</strong>: Minimal JavaScript means faster initial page loads</li>\n<li><strong>Better Core Web Vitals</strong>: Optimized for Google’s performance metrics</li>\n<li><strong>Efficient Bundling</strong>: Only ships the JavaScript you actually need</li>\n</ul>\n<h3 id=\"developer-experience\">Developer Experience</h3>\n<ul>\n<li><strong>Framework Agnostic</strong>: Use React, Vue, Svelte, or any framework you prefer</li>\n<li><strong>TypeScript Support</strong>: Built-in TypeScript support without configuration</li>\n<li><strong>Hot Module Replacement</strong>: Fast development with instant updates</li>\n</ul>\n<h3 id=\"seo-and-accessibility\">SEO and Accessibility</h3>\n<ul>\n<li><strong>Server-Side Rendering</strong>: Content is rendered on the server for better SEO</li>\n<li><strong>Static HTML</strong>: Search engines can easily crawl and index your content</li>\n<li><strong>Progressive Enhancement</strong>: Works even when JavaScript is disabled</li>\n</ul>\n<h2 id=\"getting-started\">Getting Started</h2>\n<h3 id=\"installation\">Installation</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"bash\"><code><span class=\"line\"><span style=\"color:#B392F0\">npm</span><span style=\"color:#9ECBFF\"> create</span><span style=\"color:#9ECBFF\"> astro@latest</span><span style=\"color:#9ECBFF\"> my-astro-site</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">cd</span><span style=\"color:#9ECBFF\"> my-astro-site</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">npm</span><span style=\"color:#9ECBFF\"> install</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">npm</span><span style=\"color:#9ECBFF\"> run</span><span style=\"color:#9ECBFF\"> dev</span></span></code></pre>\n<h3 id=\"project-structure\">Project Structure</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"plaintext\"><code><span class=\"line\"><span>src/</span></span>\n<span class=\"line\"><span>├── components/     # Reusable components</span></span>\n<span class=\"line\"><span>├── layouts/        # Page layouts</span></span>\n<span class=\"line\"><span>├── pages/          # File-based routing</span></span>\n<span class=\"line\"><span>└── content/        # Content collections</span></span></code></pre>\n<h3 id=\"content-collections\">Content Collections</h3>\n<p>Astro’s content collections provide type-safe content management:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"typescript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// src/content/config.ts</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">import</span><span style=\"color:#E1E4E8\"> { defineCollection, z } </span><span style=\"color:#F97583\">from</span><span style=\"color:#9ECBFF\"> 'astro:content'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">const</span><span style=\"color:#79B8FF\"> blogCollection</span><span style=\"color:#F97583\"> =</span><span style=\"color:#B392F0\"> defineCollection</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  type: </span><span style=\"color:#9ECBFF\">'content'</span><span style=\"color:#E1E4E8\">,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  schema: z.</span><span style=\"color:#B392F0\">object</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    title: z.</span><span style=\"color:#B392F0\">string</span><span style=\"color:#E1E4E8\">(),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    publishDate: z.</span><span style=\"color:#B392F0\">date</span><span style=\"color:#E1E4E8\">(),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    tags: z.</span><span style=\"color:#B392F0\">array</span><span style=\"color:#E1E4E8\">(z.</span><span style=\"color:#B392F0\">string</span><span style=\"color:#E1E4E8\">()),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">});</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">export</span><span style=\"color:#F97583\"> const</span><span style=\"color:#79B8FF\"> collections</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#9ECBFF\">  'blog'</span><span style=\"color:#E1E4E8\">: blogCollection,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">};</span></span></code></pre>\n<h2 id=\"when-to-choose-astro\">When to Choose Astro</h2>\n<p>Astro is particularly well-suited for:</p>\n<ul>\n<li><strong>Content-focused sites</strong>: Blogs, documentation, marketing sites</li>\n<li><strong>E-commerce sites</strong>: Product catalogs, landing pages</li>\n<li><strong>Portfolio sites</strong>: Showcasing work with minimal interactivity</li>\n<li><strong>Corporate websites</strong>: Company sites with occasional interactive elements</li>\n</ul>\n<h2 id=\"conclusion\">Conclusion</h2>\n<p>Astro represents a thoughtful approach to modern web development, prioritizing performance without sacrificing developer experience. Its unique architecture makes it an excellent choice for content-focused websites that need to be fast, SEO-friendly, and maintainable.</p>\n<p>Whether you’re building a personal blog, a company website, or a documentation site, Astro provides the tools and performance characteristics to create exceptional web experiences.</p>\n<p>Ready to give Astro a try? Start with their excellent documentation and join the growing community of developers who are building faster websites with less JavaScript.</p>", {"headings": 33, "localImagePaths": 78, "remoteImagePaths": 79, "frontmatter": 80, "imagePaths": 83}, [34, 37, 41, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 75], {"depth": 35, "slug": 36, "text": 14}, 1, "getting-started-with-astro-a-modern-static-site-generator", {"depth": 38, "slug": 39, "text": 40}, 2, "what-makes-astro-special", "What Makes Astro Special?", {"depth": 42, "slug": 43, "text": 44}, 3, "zero-javascript-by-default", "Zero JavaScript by <PERSON><PERSON><PERSON>", {"depth": 42, "slug": 46, "text": 47}, "component-islands-architecture", "Component Islands Architecture", {"depth": 38, "slug": 49, "text": 50}, "key-benefits", "Key Benefits", {"depth": 42, "slug": 52, "text": 53}, "performance", "Performance", {"depth": 42, "slug": 55, "text": 56}, "developer-experience", "Developer Experience", {"depth": 42, "slug": 58, "text": 59}, "seo-and-accessibility", "SEO and Accessibility", {"depth": 38, "slug": 61, "text": 62}, "getting-started", "Getting Started", {"depth": 42, "slug": 64, "text": 65}, "installation", "Installation", {"depth": 42, "slug": 67, "text": 68}, "project-structure", "Project Structure", {"depth": 42, "slug": 70, "text": 71}, "content-collections", "Content Collections", {"depth": 38, "slug": 73, "text": 74}, "when-to-choose-astro", "When to <PERSON><PERSON>", {"depth": 38, "slug": 76, "text": 77}, "conclusion", "Conclusion", [], [], {"title": 14, "description": 15, "publishDate": 81, "author": 17, "image": 18, "tags": 82, "category": 24, "featured": 25, "draft": 26}, ["Date", "2024-06-05T09:00:00.000Z"], [20, 21, 22, 23], [], "2024-06-05-getting-started-with-astro.md", "2024-05-28-modern-css-techniques", {"id": 85, "data": 87, "body": 96, "filePath": 97, "digest": 98, "rendered": 99, "legacyId": 167}, {"title": 88, "description": 89, "publishDate": 90, "author": 17, "image": 91, "tags": 92, "category": 24, "featured": 26, "draft": 26}, "Modern CSS Techniques Every Developer Should Know", "Explore the latest CSS features and techniques that can improve your web development workflow and create better user experiences.", ["Date", "2024-05-28T14:30:00.000Z"], "/images/css-techniques-cover.jpg", [93, 21, 94, 95], "CSS", "Frontend", "Design", "# Modern CSS Techniques Every Developer Should Know\n\nCSS has evolved tremendously over the past few years, introducing powerful new features that make styling more intuitive and maintainable. Let's explore some modern CSS techniques that every developer should have in their toolkit.\n\n## CSS Custom Properties (Variables)\n\nCSS custom properties have revolutionized how we handle theming and maintainable styles:\n\n```css\n:root {\n  --primary-color: #2563eb;\n  --secondary-color: #64748b;\n  --border-radius: 8px;\n  --spacing-unit: 1rem;\n}\n\n.button {\n  background-color: var(--primary-color);\n  border-radius: var(--border-radius);\n  padding: calc(var(--spacing-unit) * 0.5) var(--spacing-unit);\n}\n\n/* Dynamic theming */\n[data-theme=\"dark\"] {\n  --primary-color: #3b82f6;\n  --secondary-color: #94a3b8;\n}\n```\n\n## Container Queries\n\nContainer queries allow components to respond to their container's size rather than the viewport:\n\n```css\n.card-container {\n  container-type: inline-size;\n}\n\n@container (min-width: 400px) {\n  .card {\n    display: grid;\n    grid-template-columns: 1fr 2fr;\n    gap: 1rem;\n  }\n}\n```\n\n## CSS Grid and Subgrid\n\nCSS Grid has matured with powerful features like subgrid:\n\n```css\n.layout {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 2rem;\n}\n\n.card {\n  display: grid;\n  grid-template-rows: subgrid;\n  grid-row: span 3;\n}\n```\n\n## Logical Properties\n\nLogical properties make internationalization easier:\n\n```css\n.content {\n  /* Instead of margin-left and margin-right */\n  margin-inline: 1rem;\n  \n  /* Instead of padding-top and padding-bottom */\n  padding-block: 2rem;\n  \n  /* Instead of border-left */\n  border-inline-start: 2px solid var(--primary-color);\n}\n```\n\n## Modern Layout Techniques\n\n### Intrinsic Web Design\n```css\n.responsive-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 2rem;\n}\n```\n\n### Aspect Ratio\n```css\n.video-container {\n  aspect-ratio: 16 / 9;\n  background: #000;\n}\n\n.square-image {\n  aspect-ratio: 1;\n  object-fit: cover;\n}\n```\n\n## Advanced Selectors\n\n### :has() Pseudo-class\n```css\n/* Style a card differently if it contains an image */\n.card:has(img) {\n  grid-template-rows: auto 1fr auto;\n}\n\n/* Style a form when it has invalid inputs */\n.form:has(:invalid) {\n  border-color: red;\n}\n```\n\n### :where() and :is()\n```css\n/* Lower specificity with :where() */\n:where(h1, h2, h3) {\n  margin-block: 1em 0.5em;\n}\n\n/* Forgiving selector list with :is() */\n:is(.dark-theme, .high-contrast) .button {\n  border: 2px solid currentColor;\n}\n```\n\n## CSS Functions\n\n### clamp()\n```css\n.responsive-text {\n  font-size: clamp(1rem, 4vw, 2rem);\n}\n\n.flexible-width {\n  width: clamp(300px, 50%, 800px);\n}\n```\n\n### min() and max()\n```css\n.container {\n  width: min(100% - 2rem, 1200px);\n  margin-inline: auto;\n}\n```\n\n## Color Functions\n\n### color-mix()\n```css\n.button {\n  background: color-mix(in srgb, var(--primary-color) 80%, white);\n}\n\n.hover-state {\n  background: color-mix(in srgb, var(--primary-color), black 10%);\n}\n```\n\n## Performance Optimizations\n\n### content-visibility\n```css\n.long-content {\n  content-visibility: auto;\n  contain-intrinsic-size: 1000px;\n}\n```\n\n### will-change\n```css\n.animated-element {\n  will-change: transform;\n  transition: transform 0.3s ease;\n}\n\n.animated-element:hover {\n  transform: translateY(-4px);\n}\n```\n\n## Best Practices\n\n1. **Use Logical Properties**: Better for internationalization\n2. **Leverage Custom Properties**: Easier theming and maintenance\n3. **Embrace Container Queries**: More flexible responsive design\n4. **Optimize with Modern Functions**: Better performance and flexibility\n5. **Progressive Enhancement**: Use feature queries for newer features\n\n```css\n@supports (container-type: inline-size) {\n  /* Container query styles */\n}\n\n@supports not (aspect-ratio: 1) {\n  /* Fallback styles */\n}\n```\n\n## Conclusion\n\nModern CSS provides powerful tools for creating maintainable, performant, and flexible stylesheets. By embracing these techniques, you can write cleaner code, create better user experiences, and future-proof your projects.\n\nThe key is to gradually adopt these features while maintaining browser support for your target audience. Start with the most widely supported features and progressively enhance with newer capabilities.", "src/content/blog/2024-05-28-modern-css-techniques.md", "16ee6238eb8e7e31", {"html": 100, "metadata": 101}, "<h1 id=\"modern-css-techniques-every-developer-should-know\">Modern CSS Techniques Every Developer Should Know</h1>\n<p>CSS has evolved tremendously over the past few years, introducing powerful new features that make styling more intuitive and maintainable. Let’s explore some modern CSS techniques that every developer should have in their toolkit.</p>\n<h2 id=\"css-custom-properties-variables\">CSS Custom Properties (Variables)</h2>\n<p>CSS custom properties have revolutionized how we handle theming and maintainable styles:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">:root</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --primary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#2563eb</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --secondary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#64748b</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --border-radius</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">8</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --spacing-unit</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.button</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border-radius</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--border-radius</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  padding</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">calc</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--spacing-unit</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> 0.5</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--spacing-unit</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">/* Dynamic theming */</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">[</span><span style=\"color:#B392F0\">data-theme</span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\">\"dark\"</span><span style=\"color:#E1E4E8\">] {</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --primary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#3b82f6</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">  --secondary-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#94a3b8</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"container-queries\">Container Queries</h2>\n<p>Container queries allow components to respond to their container’s size rather than the viewport:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.card-container</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  container-type</span><span style=\"color:#E1E4E8\">: inline-size;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">@container</span><span style=\"color:#E1E4E8\"> (min-width: 400px) {</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  .card</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    grid-template-columns</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#79B8FF\"> 2</span><span style=\"color:#F97583\">fr</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    gap</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"css-grid-and-subgrid\">CSS Grid and Subgrid</h2>\n<p>CSS Grid has matured with powerful features like subgrid:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.layout</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-columns</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">repeat</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">3</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  gap</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.card</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-rows</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">subgrid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-row</span><span style=\"color:#E1E4E8\">: span </span><span style=\"color:#79B8FF\">3</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"logical-properties\">Logical Properties</h2>\n<p>Logical properties make internationalization easier:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.content</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Instead of margin-left and margin-right */</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  margin-inline</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Instead of padding-top and padding-bottom */</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  padding-block</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Instead of border-left */</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border-inline-start</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">px</span><span style=\"color:#79B8FF\"> solid</span><span style=\"color:#79B8FF\"> var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"modern-layout-techniques\">Modern Layout Techniques</h2>\n<h3 id=\"intrinsic-web-design\">Intrinsic Web Design</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.responsive-grid</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  display</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">grid</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-columns</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">repeat</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">auto-fit</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">minmax</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">300</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#E1E4E8\">));</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  gap</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"aspect-ratio\">Aspect Ratio</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.video-container</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  aspect-ratio</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">16</span><span style=\"color:#E1E4E8\"> / </span><span style=\"color:#79B8FF\">9</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">#000</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.square-image</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  aspect-ratio</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  object-fit</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">cover</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"advanced-selectors\">Advanced Selectors</h2>\n<h3 id=\"has-pseudo-class\">:has() Pseudo-class</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#6A737D\">/* Style a card differently if it contains an image */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.card:has</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#85E89D\">img</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  grid-template-rows</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">auto</span><span style=\"color:#79B8FF\"> 1</span><span style=\"color:#F97583\">fr</span><span style=\"color:#79B8FF\"> auto</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">/* Style a form when it has invalid inputs */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.form:has</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#B392F0\">:invalid</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border-color</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">red</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"where-and-is\">:where() and :is()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#6A737D\">/* Lower specificity with :where() */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">:where</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#85E89D\">h1</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#85E89D\">h2</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#85E89D\">h3</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  margin-block</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">em</span><span style=\"color:#79B8FF\"> 0.5</span><span style=\"color:#F97583\">em</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">/* Forgiving selector list with :is() */</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">:is</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#B392F0\">.dark-theme</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#B392F0\">.high-contrast</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#B392F0\">.button</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  border</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">px</span><span style=\"color:#79B8FF\"> solid</span><span style=\"color:#79B8FF\"> currentColor</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"css-functions\">CSS Functions</h2>\n<h3 id=\"clamp\">clamp()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.responsive-text</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  font-size</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">clamp</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">4</span><span style=\"color:#F97583\">vw</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.flexible-width</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  width</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">clamp</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">300</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">50</span><span style=\"color:#F97583\">%</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">800</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"min-and-max\">min() and max()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.container</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  width</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">min</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">100</span><span style=\"color:#F97583\">%</span><span style=\"color:#FFAB70\"> -</span><span style=\"color:#79B8FF\"> 2</span><span style=\"color:#F97583\">rem</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">1200</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  margin-inline</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">auto</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"color-functions\">Color Functions</h2>\n<h3 id=\"color-mix\">color-mix()</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.button</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">color-mix</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">in</span><span style=\"color:#79B8FF\"> srgb</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#79B8FF\">80</span><span style=\"color:#F97583\">%</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">white</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.hover-state</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  background</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">color-mix</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">in</span><span style=\"color:#79B8FF\"> srgb</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">var</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">--primary-color</span><span style=\"color:#E1E4E8\">), </span><span style=\"color:#79B8FF\">black</span><span style=\"color:#79B8FF\"> 10</span><span style=\"color:#F97583\">%</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"performance-optimizations\">Performance Optimizations</h2>\n<h3 id=\"content-visibility\">content-visibility</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.long-content</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  content-visibility</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">auto</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  contain-intrinsic-size</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1000</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h3 id=\"will-change\">will-change</h3>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#B392F0\">.animated-element</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  will-change</span><span style=\"color:#E1E4E8\">: transform;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  transition</span><span style=\"color:#E1E4E8\">: transform </span><span style=\"color:#79B8FF\">0.3</span><span style=\"color:#F97583\">s</span><span style=\"color:#79B8FF\"> ease</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#B392F0\">.animated-element:hover</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">  transform</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">translateY</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#79B8FF\">-4</span><span style=\"color:#F97583\">px</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"best-practices\">Best Practices</h2>\n<ol>\n<li><strong>Use Logical Properties</strong>: Better for internationalization</li>\n<li><strong>Leverage Custom Properties</strong>: Easier theming and maintenance</li>\n<li><strong>Embrace Container Queries</strong>: More flexible responsive design</li>\n<li><strong>Optimize with Modern Functions</strong>: Better performance and flexibility</li>\n<li><strong>Progressive Enhancement</strong>: Use feature queries for newer features</li>\n</ol>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto;\" tabindex=\"0\" data-language=\"css\"><code><span class=\"line\"><span style=\"color:#F97583\">@supports</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">container-type</span><span style=\"color:#E1E4E8\">: inline-size) {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Container query styles */</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">@supports</span><span style=\"color:#F97583\"> not</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">aspect-ratio</span><span style=\"color:#E1E4E8\">: </span><span style=\"color:#79B8FF\">1</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  /* Fallback styles */</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span></code></pre>\n<h2 id=\"conclusion\">Conclusion</h2>\n<p>Modern CSS provides powerful tools for creating maintainable, performant, and flexible stylesheets. By embracing these techniques, you can write cleaner code, create better user experiences, and future-proof your projects.</p>\n<p>The key is to gradually adopt these features while maintaining browser support for your target audience. Start with the most widely supported features and progressively enhance with newer capabilities.</p>", {"headings": 102, "localImagePaths": 161, "remoteImagePaths": 162, "frontmatter": 163, "imagePaths": 166}, [103, 105, 108, 111, 114, 117, 120, 123, 126, 129, 132, 135, 138, 141, 144, 147, 150, 153, 155, 157, 160], {"depth": 35, "slug": 104, "text": 88}, "modern-css-techniques-every-developer-should-know", {"depth": 38, "slug": 106, "text": 107}, "css-custom-properties-variables", "CSS Custom Properties (Variables)", {"depth": 38, "slug": 109, "text": 110}, "container-queries", "Container Queries", {"depth": 38, "slug": 112, "text": 113}, "css-grid-and-subgrid", "CSS Grid and Subgrid", {"depth": 38, "slug": 115, "text": 116}, "logical-properties", "Logical Properties", {"depth": 38, "slug": 118, "text": 119}, "modern-layout-techniques", "Modern Layout Techniques", {"depth": 42, "slug": 121, "text": 122}, "intrinsic-web-design", "Intrinsic Web Design", {"depth": 42, "slug": 124, "text": 125}, "aspect-ratio", "Aspect Ratio", {"depth": 38, "slug": 127, "text": 128}, "advanced-selectors", "Advanced Selectors", {"depth": 42, "slug": 130, "text": 131}, "has-pseudo-class", ":has() Pseudo-class", {"depth": 42, "slug": 133, "text": 134}, "where-and-is", ":where() and :is()", {"depth": 38, "slug": 136, "text": 137}, "css-functions", "CSS Functions", {"depth": 42, "slug": 139, "text": 140}, "clamp", "clamp()", {"depth": 42, "slug": 142, "text": 143}, "min-and-max", "min() and max()", {"depth": 38, "slug": 145, "text": 146}, "color-functions", "Color Functions", {"depth": 42, "slug": 148, "text": 149}, "color-mix", "color-mix()", {"depth": 38, "slug": 151, "text": 152}, "performance-optimizations", "Performance Optimizations", {"depth": 42, "slug": 154, "text": 154}, "content-visibility", {"depth": 42, "slug": 156, "text": 156}, "will-change", {"depth": 38, "slug": 158, "text": 159}, "best-practices", "Best Practices", {"depth": 38, "slug": 76, "text": 77}, [], [], {"title": 88, "description": 89, "publishDate": 164, "author": 17, "image": 91, "tags": 165, "category": 24, "featured": 26, "draft": 26}, ["Date", "2024-05-28T14:30:00.000Z"], [93, 21, 94, 95], [], "2024-05-28-modern-css-techniques.md", "projects", ["Map", 170, 171, 219, 220], "task-management-app", {"id": 170, "data": 172, "body": 186, "filePath": 187, "digest": 188, "rendered": 189, "legacyId": 218}, {"title": 173, "description": 174, "image": 175, "technologies": 176, "liveUrl": 182, "githubUrl": 183, "featured": 25, "publishDate": 184, "status": 185}, "Task Management Application", "A full-stack task management application with real-time collaboration features and intuitive user interface.", "/images/task-app-preview.jpg", [177, 178, 179, 180, 181], "React", "Node.js", "MongoDB", "Socket.io", "Express", "https://taskmanager-demo.vercel.app", "https://github.com/bagusfarisa/task-manager", ["Date", "2024-05-15T10:00:00.000Z"], "completed", "# Task Management Application\n\nA comprehensive task management solution designed for teams and individuals to organize, track, and collaborate on projects efficiently.\n\n## Key Features\n\n- **Real-time Collaboration**: Multiple users can work on the same project simultaneously\n- **Drag & Drop Interface**: Intuitive task organization with drag-and-drop functionality\n- **Project Management**: Create and manage multiple projects with different team members\n- **Progress Tracking**: Visual progress indicators and completion statistics\n- **Notifications**: Real-time notifications for task updates and deadlines\n\n## Technical Stack\n\n### Frontend\n- **React**: Component-based UI development\n- **Redux Toolkit**: State management for complex application state\n- **React Beautiful DnD**: Smooth drag-and-drop interactions\n- **Styled Components**: CSS-in-JS for component styling\n- **React Router**: Client-side routing\n\n### Backend\n- **Node.js**: Server-side JavaScript runtime\n- **Express.js**: Web application framework\n- **MongoDB**: NoSQL database for flexible data storage\n- **Socket.io**: Real-time bidirectional communication\n- **JWT**: Secure authentication and authorization\n\n## Architecture\n\nThe application follows a modern full-stack architecture:\n\n1. **Client-Server Communication**: RESTful API with real-time WebSocket connections\n2. **Database Design**: Optimized MongoDB schemas for users, projects, and tasks\n3. **Authentication**: JWT-based authentication with refresh token rotation\n4. **Real-time Updates**: Socket.io for instant collaboration features\n\n## Challenges Solved\n\n- **Concurrent Editing**: Implemented conflict resolution for simultaneous task updates\n- **Performance**: Optimized database queries and implemented efficient caching\n- **User Experience**: Created intuitive interfaces for complex project management workflows\n- **Scalability**: Designed architecture to handle growing user base and data volume\n\nThis project showcases my full-stack development capabilities and understanding of modern web application architecture.", "src/content/projects/task-management-app.md", "e235bfba360edfbe", {"html": 190, "metadata": 191}, "<h1 id=\"task-management-application\">Task Management Application</h1>\n<p>A comprehensive task management solution designed for teams and individuals to organize, track, and collaborate on projects efficiently.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Real-time Collaboration</strong>: Multiple users can work on the same project simultaneously</li>\n<li><strong>Drag &#x26; Drop Interface</strong>: Intuitive task organization with drag-and-drop functionality</li>\n<li><strong>Project Management</strong>: Create and manage multiple projects with different team members</li>\n<li><strong>Progress Tracking</strong>: Visual progress indicators and completion statistics</li>\n<li><strong>Notifications</strong>: Real-time notifications for task updates and deadlines</li>\n</ul>\n<h2 id=\"technical-stack\">Technical Stack</h2>\n<h3 id=\"frontend\">Frontend</h3>\n<ul>\n<li><strong>React</strong>: Component-based UI development</li>\n<li><strong>Redux Toolkit</strong>: State management for complex application state</li>\n<li><strong>React Beautiful DnD</strong>: Smooth drag-and-drop interactions</li>\n<li><strong>Styled Components</strong>: CSS-in-JS for component styling</li>\n<li><strong>React Router</strong>: Client-side routing</li>\n</ul>\n<h3 id=\"backend\">Backend</h3>\n<ul>\n<li><strong>Node.js</strong>: Server-side JavaScript runtime</li>\n<li><strong>Express.js</strong>: Web application framework</li>\n<li><strong>MongoDB</strong>: NoSQL database for flexible data storage</li>\n<li><strong>Socket.io</strong>: Real-time bidirectional communication</li>\n<li><strong>JWT</strong>: Secure authentication and authorization</li>\n</ul>\n<h2 id=\"architecture\">Architecture</h2>\n<p>The application follows a modern full-stack architecture:</p>\n<ol>\n<li><strong>Client-Server Communication</strong>: RESTful API with real-time WebSocket connections</li>\n<li><strong>Database Design</strong>: Optimized MongoDB schemas for users, projects, and tasks</li>\n<li><strong>Authentication</strong>: JWT-based authentication with refresh token rotation</li>\n<li><strong>Real-time Updates</strong>: Socket.io for instant collaboration features</li>\n</ol>\n<h2 id=\"challenges-solved\">Challenges Solved</h2>\n<ul>\n<li><strong>Concurrent Editing</strong>: Implemented conflict resolution for simultaneous task updates</li>\n<li><strong>Performance</strong>: Optimized database queries and implemented efficient caching</li>\n<li><strong>User Experience</strong>: Created intuitive interfaces for complex project management workflows</li>\n<li><strong>Scalability</strong>: Designed architecture to handle growing user base and data volume</li>\n</ul>\n<p>This project showcases my full-stack development capabilities and understanding of modern web application architecture.</p>", {"headings": 192, "localImagePaths": 212, "remoteImagePaths": 213, "frontmatter": 214, "imagePaths": 217}, [193, 195, 198, 201, 203, 206, 209], {"depth": 35, "slug": 194, "text": 173}, "task-management-application", {"depth": 38, "slug": 196, "text": 197}, "key-features", "Key Features", {"depth": 38, "slug": 199, "text": 200}, "technical-stack", "Technical Stack", {"depth": 42, "slug": 202, "text": 94}, "frontend", {"depth": 42, "slug": 204, "text": 205}, "backend", "Backend", {"depth": 38, "slug": 207, "text": 208}, "architecture", "Architecture", {"depth": 38, "slug": 210, "text": 211}, "challenges-solved", "Challenges Solved", [], [], {"title": 173, "description": 174, "image": 175, "technologies": 215, "liveUrl": 182, "githubUrl": 183, "featured": 25, "publishDate": 216, "status": 185}, [177, 178, 179, 180, 181], ["Date", "2024-05-15T10:00:00.000Z"], [], "task-management-app.md", "portfolio-website", {"id": 219, "data": 221, "body": 232, "filePath": 233, "digest": 234, "rendered": 235, "legacyId": 265}, {"title": 222, "description": 223, "image": 224, "technologies": 225, "liveUrl": 229, "githubUrl": 230, "featured": 25, "publishDate": 231, "status": 185}, "Portfolio Website with Astro & Decap CMS", "A modern portfolio website built with Astro framework and integrated with Decap CMS for easy content management.", "/images/portfolio-preview.jpg", [20, 226, 227, 93, 228], "TypeScript", "Decap CMS", "Vercel", "https://bagusfarisa-astro.vercel.app", "https://github.com/bagusfarisa/bagusfarisa-astro", ["Date", "2024-06-05T10:00:00.000Z"], "# Portfolio Website with Astro & Decap CMS\n\nThis portfolio website showcases my projects and blog posts, built with modern web technologies for optimal performance and easy content management.\n\n## Features\n\n- **Static Site Generation**: Built with Astro for lightning-fast performance\n- **Content Management**: Integrated with Decap CMS for easy content editing\n- **Responsive Design**: Mobile-first approach ensuring great experience on all devices\n- **SEO Optimized**: Proper meta tags, structured data, and performance optimization\n- **Modern Stack**: TypeScript, CSS custom properties, and modern JavaScript\n\n## Technical Implementation\n\n### Astro Framework\nThe site leverages Astro's unique approach to static site generation, allowing for:\n- Zero JavaScript by default\n- Component-based architecture\n- Content collections for type-safe content management\n- Excellent performance out of the box\n\n### Decap CMS Integration\nContent management is handled through Decap CMS, providing:\n- Git-based workflow\n- Rich text editing\n- Media management\n- Preview functionality\n- User-friendly interface for non-technical users\n\n### Deployment\nThe site is deployed on Vercel with:\n- Automatic deployments from Git\n- Edge network distribution\n- Optimized build process\n- Environment variable management\n\n## Development Process\n\n1. **Planning**: Defined the site structure and content requirements\n2. **Design**: Created a clean, professional design system\n3. **Development**: Built components and pages with Astro\n4. **CMS Setup**: Configured Decap CMS for content management\n5. **Testing**: Ensured cross-browser compatibility and performance\n6. **Deployment**: Set up CI/CD pipeline with Vercel\n\nThis project demonstrates my ability to work with modern web technologies while maintaining focus on performance, accessibility, and user experience.", "src/content/projects/portfolio-website.md", "004cd52c22d6ffc5", {"html": 236, "metadata": 237}, "<h1 id=\"portfolio-website-with-astro--decap-cms\">Portfolio Website with Astro &#x26; Decap CMS</h1>\n<p>This portfolio website showcases my projects and blog posts, built with modern web technologies for optimal performance and easy content management.</p>\n<h2 id=\"features\">Features</h2>\n<ul>\n<li><strong>Static Site Generation</strong>: Built with Astro for lightning-fast performance</li>\n<li><strong>Content Management</strong>: Integrated with Decap CMS for easy content editing</li>\n<li><strong>Responsive Design</strong>: Mobile-first approach ensuring great experience on all devices</li>\n<li><strong>SEO Optimized</strong>: Proper meta tags, structured data, and performance optimization</li>\n<li><strong>Modern Stack</strong>: TypeScript, CSS custom properties, and modern JavaScript</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<h3 id=\"astro-framework\">Astro Framework</h3>\n<p>The site leverages Astro’s unique approach to static site generation, allowing for:</p>\n<ul>\n<li>Zero JavaScript by default</li>\n<li>Component-based architecture</li>\n<li>Content collections for type-safe content management</li>\n<li>Excellent performance out of the box</li>\n</ul>\n<h3 id=\"decap-cms-integration\">Decap CMS Integration</h3>\n<p>Content management is handled through Decap CMS, providing:</p>\n<ul>\n<li>Git-based workflow</li>\n<li>Rich text editing</li>\n<li>Media management</li>\n<li>Preview functionality</li>\n<li>User-friendly interface for non-technical users</li>\n</ul>\n<h3 id=\"deployment\">Deployment</h3>\n<p>The site is deployed on Vercel with:</p>\n<ul>\n<li>Automatic deployments from Git</li>\n<li>Edge network distribution</li>\n<li>Optimized build process</li>\n<li>Environment variable management</li>\n</ul>\n<h2 id=\"development-process\">Development Process</h2>\n<ol>\n<li><strong>Planning</strong>: Defined the site structure and content requirements</li>\n<li><strong>Design</strong>: Created a clean, professional design system</li>\n<li><strong>Development</strong>: Built components and pages with Astro</li>\n<li><strong>CMS Setup</strong>: Configured Decap CMS for content management</li>\n<li><strong>Testing</strong>: Ensured cross-browser compatibility and performance</li>\n<li><strong>Deployment</strong>: Set up CI/CD pipeline with Vercel</li>\n</ol>\n<p>This project demonstrates my ability to work with modern web technologies while maintaining focus on performance, accessibility, and user experience.</p>", {"headings": 238, "localImagePaths": 259, "remoteImagePaths": 260, "frontmatter": 261, "imagePaths": 264}, [239, 241, 244, 247, 250, 253, 256], {"depth": 35, "slug": 240, "text": 222}, "portfolio-website-with-astro--decap-cms", {"depth": 38, "slug": 242, "text": 243}, "features", "Features", {"depth": 38, "slug": 245, "text": 246}, "technical-implementation", "Technical Implementation", {"depth": 42, "slug": 248, "text": 249}, "astro-framework", "Astro Framework", {"depth": 42, "slug": 251, "text": 252}, "decap-cms-integration", "Decap CMS Integration", {"depth": 42, "slug": 254, "text": 255}, "deployment", "Deployment", {"depth": 38, "slug": 257, "text": 258}, "development-process", "Development Process", [], [], {"title": 222, "description": 223, "image": 224, "technologies": 262, "liveUrl": 229, "githubUrl": 230, "featured": 25, "publishDate": 263, "status": 185}, [20, 226, 227, 93, 228], ["Date", "2024-06-05T10:00:00.000Z"], [], "portfolio-website.md"]