{"version": 3, "routes": [{"handle": "filesystem"}, {"src": "^/_astro/(.*)$", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "continue": true}, {"src": "^/_server-islands/([^/]+?)/?$", "dest": "_render"}, {"src": "^/_image/?$", "dest": "_render"}, {"src": "^/admin/config\\.yml/?$", "dest": "_render"}, {"src": "^/admin/status\\.json/?$", "dest": "_render"}, {"src": "^/admin/?$", "dest": "_render"}, {"src": "^/api/auth/?$", "dest": "_render"}, {"src": "^/api/callback/?$", "dest": "_render"}, {"src": "^/blog/([^/]+?)/?$", "dest": "_render"}, {"src": "^/blog/?$", "dest": "_render"}, {"src": "^/debug-auth/?$", "dest": "_render"}, {"src": "^/projects/([^/]+?)/?$", "dest": "_render"}, {"src": "^/projects/?$", "dest": "_render"}, {"src": "^/test-auth/?$", "dest": "_render"}, {"src": "^/test-callback-success/?$", "dest": "_render"}, {"src": "^/test-config/?$", "dest": "_render"}, {"src": "^/test-oauth-flow/?$", "dest": "_render"}, {"src": "^/test-oauth-flow-complete/?$", "dest": "_render"}, {"src": "^/test-oauth-simple/?$", "dest": "_render"}, {"src": "^/test-postmessage/?$", "dest": "_render"}, {"src": "^/$", "dest": "_render"}]}