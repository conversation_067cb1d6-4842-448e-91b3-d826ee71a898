import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderTemplate } from './astro/server_CADh2GTU.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                        */

const $$Astro = createAstro("https://bagusfarisa-astro.vercel.app");
const $$BlogCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$BlogCard;
  const { title, description, publishDate, image, tags, slug, featured } = Astro2.props;
  const formatDate = (date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    }).format(date);
  };
  return renderTemplate`${maybeRenderHead()}<article${addAttribute(`blog-card ${featured ? "featured" : ""}`, "class")} data-astro-cid-e3grugc2> ${image && renderTemplate`<div class="blog-image" data-astro-cid-e3grugc2> <img${addAttribute(image, "src")}${addAttribute(title, "alt")} loading="lazy" data-astro-cid-e3grugc2> </div>`} <div class="blog-content" data-astro-cid-e3grugc2> <div class="blog-meta" data-astro-cid-e3grugc2> <time${addAttribute(publishDate.toISOString(), "datetime")} data-astro-cid-e3grugc2> ${formatDate(publishDate)} </time> </div> <h3 class="blog-title" data-astro-cid-e3grugc2> <a${addAttribute(`/blog/${slug}`, "href")} data-astro-cid-e3grugc2>${title}</a> </h3> <p class="blog-description" data-astro-cid-e3grugc2>${description}</p> ${tags.length > 0 && renderTemplate`<div class="blog-tags" data-astro-cid-e3grugc2> ${tags.map((tag) => renderTemplate`<span class="tag" data-astro-cid-e3grugc2>${tag}</span>`)} </div>`} <a${addAttribute(`/blog/${slug}`, "href")} class="read-more" data-astro-cid-e3grugc2>
Read More →
</a> </div> </article> `;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/components/BlogCard.astro", void 0);

export { $$BlogCard as $ };
