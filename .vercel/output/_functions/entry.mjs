import { renderers } from './renderers.mjs';
import { c as createExports } from './chunks/entrypoint_0TvEynt-.mjs';
import { manifest } from './manifest_tDd5MoFh.mjs';

const serverIslandMap = new Map();;

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/admin/config.yml.astro.mjs');
const _page2 = () => import('./pages/admin/status.json.astro.mjs');
const _page3 = () => import('./pages/admin.astro.mjs');
const _page4 = () => import('./pages/api/auth.astro.mjs');
const _page5 = () => import('./pages/api/callback.astro.mjs');
const _page6 = () => import('./pages/blog/_slug_.astro.mjs');
const _page7 = () => import('./pages/blog.astro.mjs');
const _page8 = () => import('./pages/debug-auth.astro.mjs');
const _page9 = () => import('./pages/projects/_slug_.astro.mjs');
const _page10 = () => import('./pages/projects.astro.mjs');
const _page11 = () => import('./pages/test-auth.astro.mjs');
const _page12 = () => import('./pages/test-callback-success.astro.mjs');
const _page13 = () => import('./pages/test-config.astro.mjs');
const _page14 = () => import('./pages/test-oauth-flow.astro.mjs');
const _page15 = () => import('./pages/test-oauth-flow-complete.astro.mjs');
const _page16 = () => import('./pages/test-oauth-simple.astro.mjs');
const _page17 = () => import('./pages/test-postmessage.astro.mjs');
const _page18 = () => import('./pages/index.astro.mjs');
const pageMap = new Map([
    ["node_modules/astro/dist/assets/endpoint/generic.js", _page0],
    ["src/pages/admin/config.yml.ts", _page1],
    ["src/pages/admin/status.json.ts", _page2],
    ["src/pages/admin/index.astro", _page3],
    ["src/pages/api/auth.ts", _page4],
    ["src/pages/api/callback.ts", _page5],
    ["src/pages/blog/[slug].astro", _page6],
    ["src/pages/blog.astro", _page7],
    ["src/pages/debug-auth.ts", _page8],
    ["src/pages/projects/[slug].astro", _page9],
    ["src/pages/projects.astro", _page10],
    ["src/pages/test-auth.ts", _page11],
    ["src/pages/test-callback-success.ts", _page12],
    ["src/pages/test-config.ts", _page13],
    ["src/pages/test-oauth-flow.ts", _page14],
    ["src/pages/test-oauth-flow-complete.ts", _page15],
    ["src/pages/test-oauth-simple.ts", _page16],
    ["src/pages/test-postmessage.ts", _page17],
    ["src/pages/index.astro", _page18]
]);

const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    actions: () => import('./_noop-actions.mjs'),
    middleware: () => import('./_noop-middleware.mjs')
});
const _args = {
    "middlewareSecret": "12785905-81dc-42e1-b19c-445f94a41b12",
    "skewProtection": false
};
const _exports = createExports(_manifest, _args);
const __astrojsSsrVirtualEntry = _exports.default;

export { __astrojsSsrVirtualEntry as default, pageMap };
