export { renderers } from '../../renderers.mjs';

const GET = async ({ url }) => {
  const status = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    environment: url.hostname === "localhost" || url.hostname === "127.0.0.1" ? "development" : "production",
    origin: url.origin,
    configEndpoint: "/admin/config.yml",
    authEndpoint: "/api/auth",
    callbackEndpoint: "/api/callback",
    adminPath: "/admin/",
    cmsVersion: "3.0.0+",
    status: "healthy"
  };
  return new Response(JSON.stringify(status, null, 2), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-cache"
    }
  });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
