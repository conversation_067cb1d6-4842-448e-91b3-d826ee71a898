import { c as createAstro, a as createComponent, e as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../chunks/astro/server_CADh2GTU.mjs';
import 'kleur/colors';
import { $ as $$Layout, g as getCollection } from '../../chunks/_astro_content_CRw9M9HZ.mjs';
/* empty css                                     */
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("https://bagusfarisa-astro.vercel.app");
async function getStaticPaths() {
  const posts = await getCollection("blog");
  return posts.filter((post) => !post.data.draft).map((post) => ({
    params: { slug: post.slug },
    props: { post }
  }));
}
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { post } = Astro2.props;
  const { Content } = await post.render();
  const formatDate = (date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    }).format(date);
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${post.data.title} - Blog`, "description": post.data.description, "image": post.data.image, "data-astro-cid-4sn4zg3r": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<article class="blog-post" data-astro-cid-4sn4zg3r> <!-- Back Navigation --> <nav class="breadcrumb" data-astro-cid-4sn4zg3r> <a href="/blog" class="back-link" data-astro-cid-4sn4zg3r>← Back to Blog</a> </nav> <!-- Post Header --> <header class="post-header" data-astro-cid-4sn4zg3r> <h1 class="post-title" data-astro-cid-4sn4zg3r>${post.data.title}</h1> <div class="post-meta" data-astro-cid-4sn4zg3r> <div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Published:</span> <time${addAttribute(post.data.publishDate.toISOString(), "datetime")} data-astro-cid-4sn4zg3r> ${formatDate(post.data.publishDate)} </time> </div> ${post.data.updatedDate && renderTemplate`<div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Updated:</span> <time${addAttribute(post.data.updatedDate.toISOString(), "datetime")} data-astro-cid-4sn4zg3r> ${formatDate(post.data.updatedDate)} </time> </div>`} <div class="meta-item" data-astro-cid-4sn4zg3r> <span class="meta-label" data-astro-cid-4sn4zg3r>Author:</span> <span data-astro-cid-4sn4zg3r>${post.data.author}</span> </div> </div> <p class="post-description" data-astro-cid-4sn4zg3r>${post.data.description}</p> <!-- Tags --> ${post.data.tags.length > 0 && renderTemplate`<div class="post-tags" data-astro-cid-4sn4zg3r> <span class="tags-label" data-astro-cid-4sn4zg3r>Tags:</span> <div class="tags-list" data-astro-cid-4sn4zg3r> ${post.data.tags.map((tag) => renderTemplate`<span class="tag" data-astro-cid-4sn4zg3r>${tag}</span>`)} </div> </div>`} </header> <!-- Featured Image --> ${post.data.image && renderTemplate`<div class="post-image" data-astro-cid-4sn4zg3r> <img${addAttribute(post.data.image, "src")}${addAttribute(post.data.title, "alt")} data-astro-cid-4sn4zg3r> </div>`} <!-- Post Content --> <div class="post-content" data-astro-cid-4sn4zg3r> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-4sn4zg3r": true })} </div> <!-- Post Footer --> <footer class="post-footer" data-astro-cid-4sn4zg3r> <div class="share-section" data-astro-cid-4sn4zg3r> <h3 data-astro-cid-4sn4zg3r>Share this post</h3> <div class="share-buttons" data-astro-cid-4sn4zg3r> <a${addAttribute(`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.data.title)}&url=${encodeURIComponent(Astro2.url.href)}`, "href")} target="_blank" rel="noopener noreferrer" class="share-button twitter" data-astro-cid-4sn4zg3r>
Twitter
</a> <a${addAttribute(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(Astro2.url.href)}`, "href")} target="_blank" rel="noopener noreferrer" class="share-button linkedin" data-astro-cid-4sn4zg3r>
LinkedIn
</a> </div> </div> </footer> </article> ` })} `;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog/[slug].astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog/[slug].astro";
const $$url = "/blog/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
