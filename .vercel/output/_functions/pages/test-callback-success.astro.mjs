export { renderers } from '../renderers.mjs';

const GET = async ({ url }) => {
  const testToken = "gho_test_token_" + Math.random().toString(36).substring(2, 15);
  const provider = "github";
  const html = `<!DOCTYPE html>
<html>
<head>
  <title>Test OAuth Success</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .success {
      color: #28a745;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="spinner"></div>
    <h2 class="success">Test Authentication Successful!</h2>
    <p>Sending test token to parent window...</p>
    <p><small>Token: ${testToken}</small></p>
  </div>

  <script>
    function sendMessage() {
      console.log('🧪 Test callback: Sending message to parent window...');
      
      if (window.opener) {
        console.log('✅ Found window.opener, sending test message...');
        try {
          const message = "authorization:github:success:" + JSON.stringify({
            token: "${testToken}",
            provider: "${provider}"
          });
          
          console.log('📤 Sending message:', message);
          window.opener.postMessage(message, window.location.origin);
          console.log('✅ Test message sent successfully');
          
          // Close the popup after a short delay
          setTimeout(function() {
            console.log('🔒 Closing test popup window...');
            window.close();
          }, 2000);
        } catch (error) {
          console.error('❌ Error sending test message:', error);
        }
      } else {
        console.error('❌ No window.opener found!');
      }
    }
    
    // Send message immediately
    sendMessage();
    
    // Also send on window load as backup
    window.addEventListener('load', sendMessage);
    
    console.log('🧪 Test callback page loaded');
  <\/script>
</body>
</html>`;
  return new Response(html, {
    headers: {
      "Content-Type": "text/html; charset=UTF-8"
    }
  });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
