export { renderers } from '../renderers.mjs';

const GET = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Simple OAuth Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
  </style>
</head>
<body>
  <h1>Simple OAuth Test</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}
  </div>
  
  <h2>Test Auth Endpoint</h2>
  <button onclick="testAuthEndpoint()">Test /api/auth Endpoint</button>
  <button onclick="testAuthRedirect()">Test Auth Redirect</button>
  
  <h2>Results</h2>
  <div id="results"></div>

  <script>
    function log(message) {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = 'info';
      div.innerHTML = '<strong>' + new Date().toLocaleTimeString() + ':</strong> ' + message;
      results.appendChild(div);
    }

    async function testAuthEndpoint() {
      log('Testing /api/auth endpoint...');
      
      try {
        const response = await fetch('/api/auth', {
          method: 'GET',
          redirect: 'manual' // Don't follow redirects automatically
        });
        
        log(\`Response status: \${response.status}\`);
        log(\`Response headers: \${JSON.stringify(Object.fromEntries(response.headers))}\`);
        
        if (response.status === 302) {
          const location = response.headers.get('location');
          log(\`Redirect location: \${location}\`);
          
          if (location && location.includes('github.com/login/oauth/authorize')) {
            log('<span class="success">✓ Auth endpoint working correctly - redirects to GitHub</span>');
          } else {
            log('<span class="error">✗ Auth endpoint redirect location is incorrect</span>');
          }
        } else {
          log('<span class="error">✗ Auth endpoint should return 302 redirect</span>');
        }
      } catch (error) {
        log('<span class="error">✗ Error testing auth endpoint: ' + error.message + '</span>');
      }
    }

    function testAuthRedirect() {
      log('Testing auth redirect in new window...');
      window.open('/api/auth', 'oauth-test', 'width=600,height=600');
    }
  <\/script>
</body>
</html>
  `;
  return new Response(html, {
    headers: {
      "Content-Type": "text/html"
    }
  });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
