---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';

export async function getStaticPaths() {
  const projects = await getCollection('projects');
  return projects.map((project) => ({
    params: { slug: project.slug },
    props: { project },
  }));
}

const { project } = Astro.props;
const { Content } = await project.render();

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};
---

<Layout 
  title={`${project.data.title} - Projects`} 
  description={project.data.description}
  image={project.data.image}
>
  <article class="project-detail">
    <!-- Back Navigation -->
    <nav class="breadcrumb">
      <a href="/projects" class="back-link">← Back to Projects</a>
    </nav>

    <!-- Project Header -->
    <header class="project-header">
      <h1 class="project-title">{project.data.title}</h1>
      <p class="project-description">{project.data.description}</p>
      
      <div class="project-meta">
        <div class="meta-item">
          <span class="meta-label">Published:</span>
          <time datetime={project.data.publishDate.toISOString()}>
            {formatDate(project.data.publishDate)}
          </time>
        </div>
        <div class="meta-item">
          <span class="meta-label">Status:</span>
          <span class={`status status-${project.data.status}`}>
            {project.data.status.charAt(0).toUpperCase() + project.data.status.slice(1)}
          </span>
        </div>
      </div>

      <!-- Technologies -->
      <div class="technologies">
        <h3>Technologies Used:</h3>
        <div class="tech-list">
          {project.data.technologies.map((tech) => (
            <span class="tech-tag">{tech}</span>
          ))}
        </div>
      </div>

      <!-- Project Links -->
      <div class="project-links">
        {project.data.liveUrl && (
          <a href={project.data.liveUrl} target="_blank" rel="noopener noreferrer" class="project-link live">
            🚀 Live Demo
          </a>
        )}
        {project.data.githubUrl && (
          <a href={project.data.githubUrl} target="_blank" rel="noopener noreferrer" class="project-link github">
            📂 GitHub Repository
          </a>
        )}
      </div>
    </header>

    <!-- Project Image -->
    {project.data.image && (
      <div class="project-image">
        <img src={project.data.image} alt={project.data.title} />
      </div>
    )}

    <!-- Project Gallery -->
    {project.data.gallery && project.data.gallery.length > 0 && (
      <div class="project-gallery">
        <h3>Gallery</h3>
        <div class="gallery-grid">
          {project.data.gallery.map((image, index) => (
            <img src={image} alt={`${project.data.title} screenshot ${index + 1}`} loading="lazy" />
          ))}
        </div>
      </div>
    )}

    <!-- Project Content -->
    <div class="project-content">
      <Content />
    </div>
  </article>
</Layout>

<style>
  .project-detail {
    max-width: 800px;
    margin: 0 auto;
  }

  .breadcrumb {
    margin-bottom: 2rem;
  }

  .back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .back-link:hover {
    color: #1d4ed8;
  }

  .project-header {
    margin-bottom: 3rem;
  }

  .project-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-color);
    line-height: 1.2;
  }

  .project-description {
    font-size: 1.25rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .project-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .meta-label {
    font-weight: 600;
    color: var(--text-color);
  }

  .status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .status-completed {
    background: #dcfce7;
    color: #166534;
  }

  .status-in-progress {
    background: #fef3c7;
    color: #92400e;
  }

  .status-planned {
    background: #e0e7ff;
    color: #3730a3;
  }

  .technologies {
    margin-bottom: 2rem;
  }

  .technologies h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .tech-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .tech-tag {
    background: var(--hover-color);
    color: var(--secondary-color);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 500;
  }

  .project-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .project-link {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .project-link.live {
    background: var(--primary-color);
    color: white;
  }

  .project-link.live:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
  }

  .project-link.github {
    background: #24292e;
    color: white;
  }

  .project-link.github:hover {
    background: #1a1e22;
    transform: translateY(-2px);
  }

  .project-image {
    margin-bottom: 3rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  }

  .project-image img {
    width: 100%;
    height: auto;
    display: block;
  }

  .project-gallery {
    margin-bottom: 3rem;
  }

  .project-gallery h3 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-weight: 600;
  }

  .gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .gallery-grid img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.3s ease;
  }

  .gallery-grid img:hover {
    transform: scale(1.05);
  }

  .project-content {
    line-height: 1.7;
    color: var(--text-color);
  }

  .project-content :global(h2) {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }

  .project-content :global(h3) {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
  }

  .project-content :global(p) {
    margin-bottom: 1rem;
  }

  .project-content :global(ul),
  .project-content :global(ol) {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  .project-content :global(li) {
    margin-bottom: 0.5rem;
  }

  .project-content :global(code) {
    background: var(--hover-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
  }

  .project-content :global(pre) {
    background: var(--hover-color);
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 1rem;
  }

  @media (max-width: 768px) {
    .project-title {
      font-size: 2.5rem;
    }

    .project-description {
      font-size: 1.125rem;
    }

    .project-meta {
      flex-direction: column;
      gap: 1rem;
    }

    .project-links {
      flex-direction: column;
    }

    .project-link {
      text-align: center;
    }

    .gallery-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
