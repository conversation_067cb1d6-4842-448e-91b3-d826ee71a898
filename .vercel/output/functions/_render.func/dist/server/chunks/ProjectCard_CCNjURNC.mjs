import { c as createAstro, a as createComponent, m as maybe<PERSON><PERSON><PERSON><PERSON>, b as addAttribute, r as renderTemplate } from './astro/server_CADh2GTU.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                         */

const $$Astro = createAstro("https://bagusfarisa-astro.vercel.app");
const $$ProjectCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ProjectCard;
  const { title, description, image, technologies, liveUrl, githubUrl, slug, featured } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<article${addAttribute(`project-card ${featured ? "featured" : ""}`, "class")} data-astro-cid-mspuyifq> ${image && renderTemplate`<div class="project-image" data-astro-cid-mspuyifq> <img${addAttribute(image, "src")}${addAttribute(title, "alt")} loading="lazy" data-astro-cid-mspuyifq> </div>`} <div class="project-content" data-astro-cid-mspuyifq> <h3 class="project-title" data-astro-cid-mspuyifq> <a${addAttribute(`/projects/${slug}`, "href")} data-astro-cid-mspuyifq>${title}</a> </h3> <p class="project-description" data-astro-cid-mspuyifq>${description}</p> <div class="project-technologies" data-astro-cid-mspuyifq> ${technologies.map((tech) => renderTemplate`<span class="tech-tag" data-astro-cid-mspuyifq>${tech}</span>`)} </div> <div class="project-links" data-astro-cid-mspuyifq> ${liveUrl && renderTemplate`<a${addAttribute(liveUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link live" data-astro-cid-mspuyifq>
Live Demo
</a>`} ${githubUrl && renderTemplate`<a${addAttribute(githubUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link github" data-astro-cid-mspuyifq>
GitHub
</a>`} <a${addAttribute(`/projects/${slug}`, "href")} class="project-link details" data-astro-cid-mspuyifq>
View Details
</a> </div> </div> </article> `;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/components/ProjectCard.astro", void 0);

export { $$ProjectCard as $ };
