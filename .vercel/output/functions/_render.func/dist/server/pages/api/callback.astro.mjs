export { renderers } from '../../renderers.mjs';

const CLIENT_ID = "Ov23liYrLSc03pEujzBl";
const CLIENT_SECRET = "1c0da22a02f7c717581a628bf2a7a2513f703c56";
function renderBody(status, content) {
  const isSuccess = status === "success";
  const token = isSuccess ? content.token : "";
  const provider = isSuccess ? content.provider : "";
  const error = !isSuccess ? content.error || "Authentication failed" : "";
  const bodyContent = isSuccess ? '<div class="spinner"></div><h2 class="success">Authentication Successful!</h2><p>Completing authorization...</p>' : '<h2 class="error">Authentication Failed</h2><p>There was an error during authentication.</p>';
  const messageData = isSuccess ? JSON.stringify({ token, provider }) : JSON.stringify({ error });
  const messageType = isSuccess ? "success" : "error";
  const postMessageScript = `
    // Prepare the message data for Decap CMS
    var messageData = ${messageData};
    var messageType = "${messageType}";
    var fullMessage = "authorization:github:" + messageType + ":" + JSON.stringify(messageData);

    function sendMessage() {
      console.log('🔄 Callback: Sending message to parent window...');
      console.log('🔄 Message type:', messageType);
      console.log('🔄 Message data:', messageData);
      console.log('🔄 Full message:', fullMessage);

      if (window.opener) {
        console.log('✅ Found window.opener, sending message...');
        try {
          // Send the message to the opener window (Decap CMS admin interface)
          window.opener.postMessage(fullMessage, window.location.origin);
          console.log('✅ Message sent to opener');

          // Close the popup after a short delay
          setTimeout(function() {
            console.log('🔒 Closing popup window...');
            window.close();
          }, 1000);
        } catch (error) {
          console.error('❌ Error sending message:', error);
          // Try with wildcard origin as fallback
          try {
            window.opener.postMessage(fullMessage, '*');
            console.log('✅ Message sent with wildcard origin');
            setTimeout(function() { window.close(); }, 1000);
          } catch (e) {
            console.error('❌ Failed with wildcard origin too:', e);
          }
        }
      } else {
        console.error('❌ No window.opener found!');
      }
    }

    // Send message when ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', sendMessage);
    } else {
      sendMessage();
    }

    console.log('🎯 Callback page script loaded');
  `;
  const html = `<!DOCTYPE html>
<html>
<head>
  <title>Authorizing...</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .error {
      color: #dc3545;
    }
    .success {
      color: #28a745;
    }
  </style>
</head>
<body>
  <div class="container">
    ${bodyContent}
  </div>

  <script>
    ${postMessageScript}
  </script>
</body>
</html>`;
  return html;
}
const GET = async ({ url }) => {
  try {
    if (!CLIENT_SECRET) ;
    const code = url.searchParams.get("code");
    if (!code) {
      throw new Error("No authorization code received");
    }
    const response = await fetch("https://github.com/login/oauth/access_token", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "astro-decap-cms-oauth",
        "Accept": "application/json"
      },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code
      })
    });
    const result = await response.json();
    if (result.error) {
      const errorHtml = renderBody("error", { error: result.error_description || result.error });
      return new Response(errorHtml, {
        headers: {
          "Content-Type": "text/html; charset=UTF-8"
        },
        status: 401
      });
    }
    const token = result.access_token;
    const provider = "github";
    const successHtml = renderBody("success", { token, provider });
    return new Response(successHtml, {
      headers: {
        "Content-Type": "text/html; charset=UTF-8"
      },
      status: 200
    });
  } catch (error) {
    console.error("Callback error:", error);
    const errorHtml = renderBody("error", {
      error: error instanceof Error ? error.message : "Authentication failed"
    });
    return new Response(errorHtml, {
      headers: {
        "Content-Type": "text/html; charset=UTF-8"
      },
      status: 500
    });
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
