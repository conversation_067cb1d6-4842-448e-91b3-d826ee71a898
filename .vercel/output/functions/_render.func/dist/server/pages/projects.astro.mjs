import { a as createComponent, e as renderComponent, f as renderScript, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../chunks/astro/server_CADh2GTU.mjs';
import 'kleur/colors';
import { g as getCollection, $ as $$Layout } from '../chunks/_astro_content_CRw9M9HZ.mjs';
import { $ as $$ProjectCard } from '../chunks/ProjectCard_CCNjURNC.mjs';
/* empty css                                    */
export { renderers } from '../renderers.mjs';

const $$Projects = createComponent(async ($$result, $$props, $$slots) => {
  const allProjects = await getCollection("projects");
  const projects = allProjects.sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());
  const allTechnologies = [...new Set(projects.flatMap((project) => project.data.technologies))].sort();
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Projects - Guntur", "description": "Explore my portfolio of web development projects and applications.", "data-astro-cid-aid3sr62": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="projects-header" data-astro-cid-aid3sr62> <h1 class="page-title" data-astro-cid-aid3sr62>My Projects</h1> <p class="page-description" data-astro-cid-aid3sr62>
A collection of projects I've worked on, showcasing various technologies and approaches to problem-solving.
</p> </div> ${projects.length > 0 ? renderTemplate`<div class="projects-container" data-astro-cid-aid3sr62> <!-- Filter Section --> <div class="filters" data-astro-cid-aid3sr62> <h3 data-astro-cid-aid3sr62>Filter by Technology:</h3> <div class="filter-tags" data-astro-cid-aid3sr62> <button class="filter-tag active" data-filter="all" data-astro-cid-aid3sr62>All</button> ${allTechnologies.map((tech) => renderTemplate`<button class="filter-tag"${addAttribute(tech.toLowerCase(), "data-filter")} data-astro-cid-aid3sr62>${tech}</button>`)} </div> </div> <!-- Projects Grid --> <div class="projects-grid" id="projects-grid" data-astro-cid-aid3sr62> ${projects.map((project) => renderTemplate`<div class="project-item"${addAttribute(project.data.technologies.map((t) => t.toLowerCase()).join(","), "data-technologies")} data-astro-cid-aid3sr62> ${renderComponent($$result2, "ProjectCard", $$ProjectCard, { "title": project.data.title, "description": project.data.description, "image": project.data.image, "technologies": project.data.technologies, "liveUrl": project.data.liveUrl, "githubUrl": project.data.githubUrl, "slug": project.slug, "featured": project.data.featured, "data-astro-cid-aid3sr62": true })} </div>`)} </div> </div>` : renderTemplate`<div class="empty-state" data-astro-cid-aid3sr62> <h2 data-astro-cid-aid3sr62>No Projects Yet</h2> <p data-astro-cid-aid3sr62>Projects will appear here once they're added through the CMS.</p> </div>`}` })}  ${renderScript($$result, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro";
const $$url = "/projects";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Projects,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
