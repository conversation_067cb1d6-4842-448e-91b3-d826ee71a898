export { renderers } from '../renderers.mjs';

const GET = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>OAuth Flow Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
    #log { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; max-height: 300px; overflow-y: auto; }
    .log-entry { margin: 5px 0; padding: 5px; border-left: 3px solid #007bff; }
    iframe { width: 100%; height: 400px; border: 1px solid #ccc; margin: 10px 0; }
  </style>
</head>
<body>
  <h1>OAuth Flow Test</h1>
  
  <div class="info">
    <strong>Testing OAuth endpoints and flow</strong><br>
    This page tests the complete OAuth authentication flow for Decap CMS.
  </div>
  
  <h2>1. Test Auth Endpoint Direct Access</h2>
  <button onclick="testAuthDirect()">Open /api/auth in New Tab</button>
  <button onclick="testAuthIframe()">Load /api/auth in iframe</button>
  <div id="iframe-container"></div>
  
  <h2>2. Test OAuth Popup (Decap CMS Style)</h2>
  <button onclick="testOAuthPopup()">Test OAuth Popup</button>
  <button onclick="testAdminInterface()">Open Admin Interface</button>
  
  <h2>3. Endpoint Status</h2>
  <div id="endpoint-status">Checking endpoints...</div>
  
  <h2>4. Event Log</h2>
  <button onclick="clearLog()">Clear Log</button>
  <div id="log"></div>
  
  <script>
    let popup = null;
    
    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const entry = document.createElement('div');
      entry.className = 'log-entry';
      entry.innerHTML = \`[\${new Date().toLocaleTimeString()}] \${message}\`;
      logDiv.appendChild(entry);
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }
    
    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }
    
    function testAuthDirect() {
      log('Opening /api/auth in new tab...');
      window.open('/api/auth', '_blank');
    }
    
    function testAuthIframe() {
      log('Loading /api/auth in iframe...');
      const container = document.getElementById('iframe-container');
      container.innerHTML = '<iframe src="/api/auth"></iframe>';
    }
    
    function testOAuthPopup() {
      log('Testing OAuth popup (simulating Decap CMS behavior)...');
      
      if (popup && !popup.closed) {
        popup.close();
      }
      
      popup = window.open('/api/auth', 'oauth-popup', 'width=600,height=600,scrollbars=yes,resizable=yes');
      
      if (!popup) {
        log('ERROR: Popup was blocked by browser', 'error');
        return;
      }
      
      log('✓ Popup opened successfully');
      
      // Monitor popup
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          log('Popup was closed');
          clearInterval(checkClosed);
        }
      }, 1000);
      
      // Listen for OAuth messages
      const messageHandler = function(event) {
        log(\`Received message from popup: \${JSON.stringify(event.data)}\`);
        
        if (event.data && typeof event.data === 'string' && event.data.includes('authorization:github:')) {
          if (event.data.includes(':success:')) {
            log('✅ OAuth SUCCESS! Token received from popup.', 'success');
          } else if (event.data.includes(':error:')) {
            log('❌ OAuth ERROR received from popup.', 'error');
          }
        }
      };
      
      window.addEventListener('message', messageHandler);
      
      // Clean up after 30 seconds
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        if (popup && !popup.closed) {
          popup.close();
        }
      }, 30000);
    }
    
    function testAdminInterface() {
      log('Opening admin interface...');
      window.open('/admin/', '_blank');
    }
    
    // Check endpoints on page load
    async function checkEndpoints() {
      const statusDiv = document.getElementById('endpoint-status');
      let status = '';
      
      // Test /api/auth
      try {
        const authResponse = await fetch('/api/auth');
        const authText = await authResponse.text();
        
        if (authResponse.ok) {
          status += '<div class="success">✓ /api/auth is accessible</div>';
          
          // Check if it contains the expected content
          if (authText.includes('Redirecting to GitHub') && authText.includes('github.com/login/oauth/authorize')) {
            status += '<div class="success">✓ /api/auth contains correct redirect content</div>';
            log('/api/auth endpoint is working correctly');
          } else {
            status += '<div class="error">✗ /api/auth content is incorrect</div>';
            log('/api/auth endpoint content is wrong');
          }
        } else {
          status += \`<div class="error">✗ /api/auth returned status: \${authResponse.status}</div>\`;
          log(\`/api/auth endpoint error: \${authResponse.status}\`);
        }
      } catch (error) {
        status += \`<div class="error">✗ /api/auth error: \${error.message}</div>\`;
        log(\`/api/auth endpoint error: \${error.message}\`);
      }
      
      // Test /api/callback
      try {
        const callbackResponse = await fetch('/api/callback');
        if (callbackResponse.status === 500) {
          // Expected - no code parameter
          status += '<div class="success">✓ /api/callback is accessible (expected error without code)</div>';
          log('/api/callback endpoint is working (expected error without auth code)');
        } else {
          status += \`<div class="info">? /api/callback returned unexpected status: \${callbackResponse.status}</div>\`;
        }
      } catch (error) {
        status += \`<div class="error">✗ /api/callback error: \${error.message}</div>\`;
      }
      
      // Test admin config
      try {
        const configResponse = await fetch('/admin/config.yml');
        const configText = await configResponse.text();
        if (configResponse.ok) {
          status += '<div class="success">✓ Admin config is accessible</div>';
          
          if (configText.includes('auth_endpoint: /api/auth')) {
            status += '<div class="success">✓ Config points to correct auth endpoint</div>';
          } else {
            status += '<div class="error">✗ Config does not point to /api/auth</div>';
          }
        }
      } catch (error) {
        status += \`<div class="error">✗ Admin config error: \${error.message}</div>\`;
      }
      
      statusDiv.innerHTML = status;
    }
    
    // Initialize
    log('OAuth Flow Test page loaded');
    checkEndpoints();
  <\/script>
</body>
</html>`;
  return new Response(html, {
    status: 200,
    headers: {
      "Content-Type": "text/html; charset=UTF-8"
    }
  });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
