import { c as createAstro, a as createComponent, e as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../chunks/astro/server_CADh2GTU.mjs';
import 'kleur/colors';
import { $ as $$Layout, g as getCollection } from '../../chunks/_astro_content_CRw9M9HZ.mjs';
/* empty css                                     */
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("https://bagusfarisa-astro.vercel.app");
async function getStaticPaths() {
  const projects = await getCollection("projects");
  return projects.map((project) => ({
    params: { slug: project.slug },
    props: { project }
  }));
}
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { project } = Astro2.props;
  const { Content } = await project.render();
  const formatDate = (date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    }).format(date);
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${project.data.title} - Projects`, "description": project.data.description, "image": project.data.image, "data-astro-cid-ovxcmftc": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<article class="project-detail" data-astro-cid-ovxcmftc> <!-- Back Navigation --> <nav class="breadcrumb" data-astro-cid-ovxcmftc> <a href="/projects" class="back-link" data-astro-cid-ovxcmftc>← Back to Projects</a> </nav> <!-- Project Header --> <header class="project-header" data-astro-cid-ovxcmftc> <h1 class="project-title" data-astro-cid-ovxcmftc>${project.data.title}</h1> <p class="project-description" data-astro-cid-ovxcmftc>${project.data.description}</p> <div class="project-meta" data-astro-cid-ovxcmftc> <div class="meta-item" data-astro-cid-ovxcmftc> <span class="meta-label" data-astro-cid-ovxcmftc>Published:</span> <time${addAttribute(project.data.publishDate.toISOString(), "datetime")} data-astro-cid-ovxcmftc> ${formatDate(project.data.publishDate)} </time> </div> <div class="meta-item" data-astro-cid-ovxcmftc> <span class="meta-label" data-astro-cid-ovxcmftc>Status:</span> <span${addAttribute(`status status-${project.data.status}`, "class")} data-astro-cid-ovxcmftc> ${project.data.status.charAt(0).toUpperCase() + project.data.status.slice(1)} </span> </div> </div> <!-- Technologies --> <div class="technologies" data-astro-cid-ovxcmftc> <h3 data-astro-cid-ovxcmftc>Technologies Used:</h3> <div class="tech-list" data-astro-cid-ovxcmftc> ${project.data.technologies.map((tech) => renderTemplate`<span class="tech-tag" data-astro-cid-ovxcmftc>${tech}</span>`)} </div> </div> <!-- Project Links --> <div class="project-links" data-astro-cid-ovxcmftc> ${project.data.liveUrl && renderTemplate`<a${addAttribute(project.data.liveUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link live" data-astro-cid-ovxcmftc>
🚀 Live Demo
</a>`} ${project.data.githubUrl && renderTemplate`<a${addAttribute(project.data.githubUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link github" data-astro-cid-ovxcmftc>
📂 GitHub Repository
</a>`} </div> </header> <!-- Project Image --> ${project.data.image && renderTemplate`<div class="project-image" data-astro-cid-ovxcmftc> <img${addAttribute(project.data.image, "src")}${addAttribute(project.data.title, "alt")} data-astro-cid-ovxcmftc> </div>`} <!-- Project Gallery --> ${project.data.gallery && project.data.gallery.length > 0 && renderTemplate`<div class="project-gallery" data-astro-cid-ovxcmftc> <h3 data-astro-cid-ovxcmftc>Gallery</h3> <div class="gallery-grid" data-astro-cid-ovxcmftc> ${project.data.gallery.map((image, index) => renderTemplate`<img${addAttribute(image, "src")}${addAttribute(`${project.data.title} screenshot ${index + 1}`, "alt")} loading="lazy" data-astro-cid-ovxcmftc>`)} </div> </div>`} <!-- Project Content --> <div class="project-content" data-astro-cid-ovxcmftc> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-ovxcmftc": true })} </div> </article> ` })} `;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects/[slug].astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects/[slug].astro";
const $$url = "/projects/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
