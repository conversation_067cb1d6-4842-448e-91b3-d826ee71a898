export { renderers } from '../renderers.mjs';

const GET = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Debug OAuth Flow</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
    #log { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; max-height: 300px; overflow-y: auto; }
    .log-entry { margin: 5px 0; padding: 5px; border-left: 3px solid #007bff; }
  </style>
</head>
<body>
  <h1>Debug OAuth Flow</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}
  </div>
  
  <h2>Test Different Auth Flows</h2>
  <button onclick="testAuthPopup()">Test Auth Popup (Decap CMS style)</button>
  <button onclick="testAuthDirect()">Test Auth Direct</button>
  <button onclick="testAuthWithProvider()">Test Auth with Provider</button>
  <button onclick="clearLog()">Clear Log</button>
  
  <h2>Configuration Status</h2>
  <div id="config-status">Checking...</div>
  
  <h2>Event Log</h2>
  <div id="log"></div>
  
  <script>
    let popup = null;
    
    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const entry = document.createElement('div');
      entry.className = 'log-entry';
      entry.innerHTML = \`[\${new Date().toLocaleTimeString()}] \${message}\`;
      logDiv.appendChild(entry);
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }
    
    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }
    
    function testAuthPopup() {
      log('Opening auth popup...');
      
      if (popup && !popup.closed) {
        popup.close();
      }
      
      popup = window.open('/auth', 'oauth-test', 'width=600,height=600,scrollbars=yes,resizable=yes');
      
      if (!popup) {
        log('ERROR: Popup was blocked by browser', 'error');
        return;
      }
      
      log('Popup opened successfully');
      
      // Monitor popup
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          log('Popup was closed');
          clearInterval(checkClosed);
        }
      }, 1000);
    }
    
    function testAuthDirect() {
      log('Opening auth in new tab...');
      window.open('/auth', '_blank');
    }
    
    function testAuthWithProvider() {
      log('Opening auth with provider parameter...');
      window.open('/auth?provider=github', '_blank');
    }
    
    // Listen for messages from popup
    window.addEventListener('message', function(event) {
      log(\`Received message from \${event.origin}: \${JSON.stringify(event.data)}\`);
      
      if (event.data && typeof event.data === 'string' && event.data.includes('authorization:github:')) {
        if (event.data.includes(':success:')) {
          log('✓ OAuth SUCCESS! Token received.', 'success');
        } else if (event.data.includes(':error:')) {
          log('✗ OAuth ERROR received.', 'error');
        }
      } else if (event.data && event.data.type && event.data.type.includes('authorization')) {
        log('✓ Authorization message received', 'success');
      }
    });
    
    // Check endpoints
    async function checkEndpoints() {
      const statusDiv = document.getElementById('config-status');
      let status = '';
      
      try {
        const authResponse = await fetch('/auth');
        if (authResponse.ok) {
          status += '<div class="success">✓ /auth endpoint accessible</div>';
          log('/auth endpoint is working');
        } else {
          status += \`<div class="error">✗ /auth returned status: \${authResponse.status}</div>\`;
          log(\`/auth endpoint error: \${authResponse.status}\`);
        }
      } catch (error) {
        status += \`<div class="error">✗ /auth error: \${error.message}</div>\`;
        log(\`/auth endpoint error: \${error.message}\`);
      }
      
      try {
        const configResponse = await fetch('/admin/config.yml');
        const configText = await configResponse.text();
        if (configResponse.ok) {
          status += '<div class="success">✓ Config endpoint accessible</div>';
          log('Config endpoint is working');
          
          // Check if config contains base_url
          if (configText.includes('base_url:')) {
            status += '<div class="success">✓ Config contains base_url</div>';
            log('Config contains base_url');
          } else {
            status += '<div class="error">✗ Config missing base_url</div>';
            log('Config missing base_url');
          }
          
          // Show relevant config lines
          const lines = configText.split('\\n').slice(0, 10);
          status += \`<div class="info"><strong>Config preview:</strong><br><pre>\${lines.join('\\n')}</pre></div>\`;
        } else {
          status += \`<div class="error">✗ Config returned status: \${configResponse.status}</div>\`;
        }
      } catch (error) {
        status += \`<div class="error">✗ Config error: \${error.message}</div>\`;
      }
      
      statusDiv.innerHTML = status;
    }
    
    // Initialize
    log('Debug page loaded');
    checkEndpoints();
  <\/script>
</body>
</html>`;
  return new Response(html, {
    status: 200,
    headers: {
      "Content-Type": "text/html"
    }
  });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
