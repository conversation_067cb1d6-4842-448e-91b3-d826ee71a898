import { a as createComponent, r as renderTemplate, d as renderHead } from '../chunks/astro/server_CADh2GTU.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate(_a || (_a = __template(['<html data-astro-cid-u2h3djql> <head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Content Manager</title><meta name="robots" content="noindex">', `</head> <body data-astro-cid-u2h3djql> <!-- Loading indicator --> <div id="cms-loading" class="cms-loading" data-astro-cid-u2h3djql> <div data-astro-cid-u2h3djql> <div class="spinner" data-astro-cid-u2h3djql></div> <p data-astro-cid-u2h3djql>Loading Content Manager...</p> </div> </div> <!-- CMS will mount here --> <div id="nc-root" data-astro-cid-u2h3djql></div> <!-- Decap CMS Script with error handling --> <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js" onerror="console.error('Failed to load Decap CMS script'); document.getElementById('cms-loading').innerHTML='<div style=&quot;text-align:center;color:#dc3545;&quot;><h2>Failed to load CMS</h2><p>Please check your internet connection and reload the page.</p></div>';"><\/script> <script>
    (function() {
      'use strict';

      // Prevent multiple script executions
      if (window.CMS_SCRIPT_LOADED) {
        console.log('\u{1F504} CMS script already loaded, skipping...');
        return;
      }
      window.CMS_SCRIPT_LOADED = true;

      // Global state management with more robust tracking
      window.CMS_STATE = {
        initialized: false,
        initializing: false,
        retryCount: 0,
        maxRetries: 2,
        lastInitTime: 0,
        minInitInterval: 10000 // 10 seconds minimum between init attempts
      };

      function hideLoading() {
        const loading = document.getElementById('cms-loading');
        if (loading) {
          loading.classList.add('hidden');
        }
      }

      function showError(message) {
        hideLoading();
        const root = document.getElementById('nc-root');
        if (root) {
          root.innerHTML = \`
            <div style="padding: 2rem; text-align: center; color: #dc3545;">
              <h2>CMS Loading Error</h2>
              <p>\${message}</p>
              <button onclick="location.reload()" style="padding: 0.5rem 1rem; margin-top: 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Reload Page
              </button>
            </div>
          \`;
        }
      }

      function cleanupExistingCMS() {
        // More thorough cleanup of existing CMS elements
        const selectors = [
          '[data-slate-editor]',
          '.cms-editor-wrapper',
          '.nc-app-container',
          '#nc-root > *'
        ];

        selectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(el => {
            try {
              if (el.parentNode) {
                el.parentNode.removeChild(el);
              }
            } catch (e) {
              console.warn('Could not remove element:', e);
            }
          });
        });

        // Clear the root container
        const root = document.getElementById('nc-root');
        if (root) {
          root.innerHTML = '';
        }
      }

      function initializeCMS() {
        const now = Date.now();

        console.log('\u{1F680} Starting CMS initialization...');

        // Check if already initialized
        if (window.CMS_STATE.initialized) {
          console.log('\u2705 CMS already initialized');
          hideLoading();
          return;
        }

        // Check if initializing too frequently
        if (now - window.CMS_STATE.lastInitTime < window.CMS_STATE.minInitInterval) {
          console.log('\u23F3 Too soon to retry initialization');
          return;
        }

        // Check if already initializing
        if (window.CMS_STATE.initializing) {
          console.log('\u23F3 CMS initialization already in progress');
          return;
        }

        window.CMS_STATE.initializing = true;
        window.CMS_STATE.lastInitTime = now;

        // Verify CMS is available
        if (typeof window.CMS === 'undefined') {
          console.error('\u274C Decap CMS not loaded');
          window.CMS_STATE.initializing = false;
          showError('Decap CMS failed to load. Please check your internet connection.');
          return;
        }

        try {
          console.log('\u{1F9F9} Cleaning up existing CMS elements...');
          cleanupExistingCMS();

          console.log('\u{1F527} Initializing Decap CMS...');

          // Initialize with minimal config to reduce conflicts
          window.CMS.init({
            config: {
              load_config_file: '/admin/config.yml'
            }
          });

          // Set up event listeners with error handling
          try {
            window.CMS.registerEventListener({
              name: 'login',
              handler: () => {
                console.log('\u2705 CMS login successful');
                window.CMS_STATE.initialized = true;
                window.CMS_STATE.initializing = false;
                hideLoading();
              }
            });
          } catch (e) {
            console.warn('Could not register login event listener:', e);
          }

          // Fallback success detection
          setTimeout(() => {
            if (!window.CMS_STATE.initialized) {
              // Check if CMS UI elements are present
              const cmsElements = document.querySelectorAll('[class*="cms"], [class*="nc-"]');
              if (cmsElements.length > 0) {
                console.log('\u2705 CMS initialization detected via DOM elements');
                window.CMS_STATE.initialized = true;
                window.CMS_STATE.initializing = false;
                hideLoading();
              } else {
                console.log('\u23F3 CMS still initializing...');
                window.CMS_STATE.initializing = false;
              }
            }
          }, 5000);

        } catch (error) {
          console.error('\u274C CMS initialization error:', error);
          window.CMS_STATE.initializing = false;

          // Retry logic with exponential backoff
          if (window.CMS_STATE.retryCount < window.CMS_STATE.maxRetries) {
            window.CMS_STATE.retryCount++;
            const delay = Math.pow(2, window.CMS_STATE.retryCount) * 2000; // 4s, 8s
            console.log(\`\u{1F504} Retrying CMS initialization in \${delay/1000}s (\${window.CMS_STATE.retryCount}/\${window.CMS_STATE.maxRetries})\`);
            setTimeout(initializeCMS, delay);
          } else {
            showError(\`Failed to initialize CMS after \${window.CMS_STATE.maxRetries} attempts. Error: \${error.message}\`);
          }
        }
      }

      // Wait for DOM and CMS script to be ready
      function waitForCMS() {
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', waitForCMS);
          return;
        }

        if (typeof window.CMS === 'undefined') {
          console.log('\u23F3 Waiting for Decap CMS to load...');
          setTimeout(waitForCMS, 200);
          return;
        }

        // Ensure DOM is fully ready before initializing
        setTimeout(initializeCMS, 1000);
      }

      // Global error handler for DOM manipulation errors
      window.addEventListener('error', function(event) {
        if (event.error && event.error.message &&
            event.error.message.includes('removeChild') &&
            event.error.message.includes('not a child')) {
          console.warn('\u{1F527} Detected removeChild DOM error, attempting CMS recovery...');

          // Reset CMS state and try again
          if (!window.CMS_STATE.initialized) {
            window.CMS_STATE.initializing = false;
            window.CMS_STATE.retryCount = 0;

            setTimeout(() => {
              cleanupExistingCMS();
              initializeCMS();
            }, 2000);
          }

          // Prevent the error from propagating
          event.preventDefault();
          return false;
        }
      });

      // Start the initialization process
      waitForCMS();

    })();
  <\/script> </body> </html>`], ['<html data-astro-cid-u2h3djql> <head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Content Manager</title><meta name="robots" content="noindex">', `</head> <body data-astro-cid-u2h3djql> <!-- Loading indicator --> <div id="cms-loading" class="cms-loading" data-astro-cid-u2h3djql> <div data-astro-cid-u2h3djql> <div class="spinner" data-astro-cid-u2h3djql></div> <p data-astro-cid-u2h3djql>Loading Content Manager...</p> </div> </div> <!-- CMS will mount here --> <div id="nc-root" data-astro-cid-u2h3djql></div> <!-- Decap CMS Script with error handling --> <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js" onerror="console.error('Failed to load Decap CMS script'); document.getElementById('cms-loading').innerHTML='<div style=&quot;text-align:center;color:#dc3545;&quot;><h2>Failed to load CMS</h2><p>Please check your internet connection and reload the page.</p></div>';"><\/script> <script>
    (function() {
      'use strict';

      // Prevent multiple script executions
      if (window.CMS_SCRIPT_LOADED) {
        console.log('\u{1F504} CMS script already loaded, skipping...');
        return;
      }
      window.CMS_SCRIPT_LOADED = true;

      // Global state management with more robust tracking
      window.CMS_STATE = {
        initialized: false,
        initializing: false,
        retryCount: 0,
        maxRetries: 2,
        lastInitTime: 0,
        minInitInterval: 10000 // 10 seconds minimum between init attempts
      };

      function hideLoading() {
        const loading = document.getElementById('cms-loading');
        if (loading) {
          loading.classList.add('hidden');
        }
      }

      function showError(message) {
        hideLoading();
        const root = document.getElementById('nc-root');
        if (root) {
          root.innerHTML = \\\`
            <div style="padding: 2rem; text-align: center; color: #dc3545;">
              <h2>CMS Loading Error</h2>
              <p>\\\${message}</p>
              <button onclick="location.reload()" style="padding: 0.5rem 1rem; margin-top: 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Reload Page
              </button>
            </div>
          \\\`;
        }
      }

      function cleanupExistingCMS() {
        // More thorough cleanup of existing CMS elements
        const selectors = [
          '[data-slate-editor]',
          '.cms-editor-wrapper',
          '.nc-app-container',
          '#nc-root > *'
        ];

        selectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(el => {
            try {
              if (el.parentNode) {
                el.parentNode.removeChild(el);
              }
            } catch (e) {
              console.warn('Could not remove element:', e);
            }
          });
        });

        // Clear the root container
        const root = document.getElementById('nc-root');
        if (root) {
          root.innerHTML = '';
        }
      }

      function initializeCMS() {
        const now = Date.now();

        console.log('\u{1F680} Starting CMS initialization...');

        // Check if already initialized
        if (window.CMS_STATE.initialized) {
          console.log('\u2705 CMS already initialized');
          hideLoading();
          return;
        }

        // Check if initializing too frequently
        if (now - window.CMS_STATE.lastInitTime < window.CMS_STATE.minInitInterval) {
          console.log('\u23F3 Too soon to retry initialization');
          return;
        }

        // Check if already initializing
        if (window.CMS_STATE.initializing) {
          console.log('\u23F3 CMS initialization already in progress');
          return;
        }

        window.CMS_STATE.initializing = true;
        window.CMS_STATE.lastInitTime = now;

        // Verify CMS is available
        if (typeof window.CMS === 'undefined') {
          console.error('\u274C Decap CMS not loaded');
          window.CMS_STATE.initializing = false;
          showError('Decap CMS failed to load. Please check your internet connection.');
          return;
        }

        try {
          console.log('\u{1F9F9} Cleaning up existing CMS elements...');
          cleanupExistingCMS();

          console.log('\u{1F527} Initializing Decap CMS...');

          // Initialize with minimal config to reduce conflicts
          window.CMS.init({
            config: {
              load_config_file: '/admin/config.yml'
            }
          });

          // Set up event listeners with error handling
          try {
            window.CMS.registerEventListener({
              name: 'login',
              handler: () => {
                console.log('\u2705 CMS login successful');
                window.CMS_STATE.initialized = true;
                window.CMS_STATE.initializing = false;
                hideLoading();
              }
            });
          } catch (e) {
            console.warn('Could not register login event listener:', e);
          }

          // Fallback success detection
          setTimeout(() => {
            if (!window.CMS_STATE.initialized) {
              // Check if CMS UI elements are present
              const cmsElements = document.querySelectorAll('[class*="cms"], [class*="nc-"]');
              if (cmsElements.length > 0) {
                console.log('\u2705 CMS initialization detected via DOM elements');
                window.CMS_STATE.initialized = true;
                window.CMS_STATE.initializing = false;
                hideLoading();
              } else {
                console.log('\u23F3 CMS still initializing...');
                window.CMS_STATE.initializing = false;
              }
            }
          }, 5000);

        } catch (error) {
          console.error('\u274C CMS initialization error:', error);
          window.CMS_STATE.initializing = false;

          // Retry logic with exponential backoff
          if (window.CMS_STATE.retryCount < window.CMS_STATE.maxRetries) {
            window.CMS_STATE.retryCount++;
            const delay = Math.pow(2, window.CMS_STATE.retryCount) * 2000; // 4s, 8s
            console.log(\\\`\u{1F504} Retrying CMS initialization in \\\${delay/1000}s (\\\${window.CMS_STATE.retryCount}/\\\${window.CMS_STATE.maxRetries})\\\`);
            setTimeout(initializeCMS, delay);
          } else {
            showError(\\\`Failed to initialize CMS after \\\${window.CMS_STATE.maxRetries} attempts. Error: \\\${error.message}\\\`);
          }
        }
      }

      // Wait for DOM and CMS script to be ready
      function waitForCMS() {
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', waitForCMS);
          return;
        }

        if (typeof window.CMS === 'undefined') {
          console.log('\u23F3 Waiting for Decap CMS to load...');
          setTimeout(waitForCMS, 200);
          return;
        }

        // Ensure DOM is fully ready before initializing
        setTimeout(initializeCMS, 1000);
      }

      // Global error handler for DOM manipulation errors
      window.addEventListener('error', function(event) {
        if (event.error && event.error.message &&
            event.error.message.includes('removeChild') &&
            event.error.message.includes('not a child')) {
          console.warn('\u{1F527} Detected removeChild DOM error, attempting CMS recovery...');

          // Reset CMS state and try again
          if (!window.CMS_STATE.initialized) {
            window.CMS_STATE.initializing = false;
            window.CMS_STATE.retryCount = 0;

            setTimeout(() => {
              cleanupExistingCMS();
              initializeCMS();
            }, 2000);
          }

          // Prevent the error from propagating
          event.preventDefault();
          return false;
        }
      });

      // Start the initialization process
      waitForCMS();

    })();
  <\/script> </body> </html>`])), renderHead());
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro";
const $$url = "/admin";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
