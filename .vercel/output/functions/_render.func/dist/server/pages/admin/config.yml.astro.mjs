export { renderers } from '../../renderers.mjs';

let configCache = null;
const CACHE_DURATION = 5e3;
const GET = async ({ url }) => {
  try {
    const now = Date.now();
    if (configCache && configCache.origin === url.origin && now - configCache.timestamp < CACHE_DURATION) {
      console.log("📋 Serving cached config for:", url.origin);
      return new Response(configCache.content, {
        status: 200,
        headers: {
          "Content-Type": "text/yaml",
          "Cache-Control": "public, max-age=5"
        }
      });
    }
    console.log("🔧 Generating fresh config for:", url.origin);
    const isLocalhost = url.hostname === "localhost" || url.hostname === "127.0.0.1";
    const siteOrigin = isLocalhost ? url.origin : "https://bagusfarisa.vercel.app";
    console.log("Environment:", isLocalhost ? "localhost" : "production");
    console.log("Using origin:", siteOrigin);
    const configContent = `backend:
  name: github
  repo: bagusfarisa/bagusfarisa-astro
  branch: main
  site_domain: ${siteOrigin}
  base_url: ${siteOrigin}
  auth_endpoint: api/auth

# Uncomment below to enable drafts
# publish_mode: editorial_workflow

media_folder: public/images/uploads # Media files will be stored in the repo under public/images/uploads
public_folder: /images/uploads # The src attribute for uploaded media will begin with /images/uploads

collections:
  - name: "projects" # Used in routes, e.g., /admin/collections/projects
    label: "Projects" # Used in the UI
    folder: "src/content/projects" # The path to the folder where the documents are stored
    create: true # Allow users to create new documents in this collection
    slug: "{{slug}}" # Filename template, e.g., YYYY-MM-DD-title.md
    fields: # The fields for each document, usually in front matter
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Featured Image", name: "image", widget: "image", required: false }
      - label: "Gallery"
        name: "gallery"
        widget: "list"
        required: false
        field: { label: "Image", name: "image", widget: "image" }
      - label: "Technologies"
        name: "technologies"
        widget: "list"
        default: []
        field: { label: "Technology", name: "tech", widget: "string" }
      - { label: "Live URL", name: "liveUrl", widget: "string", required: false }
      - { label: "GitHub URL", name: "githubUrl", widget: "string", required: false }
      - { label: "Featured", name: "featured", widget: "boolean", default: false }
      - { label: "Publish Date", name: "publishDate", widget: "datetime" }
      - label: "Status"
        name: "status"
        widget: "select"
        options: ["completed", "in-progress", "planned"]
        default: "completed"
      - { label: "Body", name: "body", widget: "markdown" }

  - name: "blog" # Used in routes, e.g., /admin/collections/blog
    label: "Blog" # Used in the UI
    folder: "src/content/blog" # The path to the folder where the documents are stored
    create: true # Allow users to create new documents in this collection
    slug: "{{year}}-{{month}}-{{day}}-{{slug}}" # Filename template, e.g., YYYY-MM-DD-title.md
    fields: # The fields for each document, usually in front matter
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Publish Date", name: "publishDate", widget: "datetime" }
      - { label: "Updated Date", name: "updatedDate", widget: "datetime", required: false }
      - { label: "Author", name: "author", widget: "string", default: "Guntur" }
      - { label: "Featured Image", name: "image", widget: "image", required: false }
      - label: "Tags"
        name: "tags"
        widget: "list"
        default: []
        field: { label: "Tag", name: "tag", widget: "string" }
      - { label: "Category", name: "category", widget: "string", required: false }
      - { label: "Featured", name: "featured", widget: "boolean", default: false }
      - { label: "Draft", name: "draft", widget: "boolean", default: false }
      - { label: "Body", name: "body", widget: "markdown" }

  - name: "pages"
    label: "Pages"
    files:
      - label: "Home Page"
        name: "home"
        file: "src/content/pages/home.md"
        fields:
          - { label: "Title", name: "title", widget: "string" }
          - { label: "Hero Title", name: "heroTitle", widget: "string" }
          - { label: "Hero Subtitle", name: "heroSubtitle", widget: "string" }
          - { label: "Hero Description", name: "heroDescription", widget: "text" }
          - { label: "Body", name: "body", widget: "markdown" }
`;
    configCache = {
      content: configContent,
      timestamp: now,
      origin: url.origin
    };
    return new Response(configContent, {
      status: 200,
      headers: {
        "Content-Type": "text/yaml",
        "Cache-Control": "public, max-age=5",
        "ETag": `"${now}-${url.origin}"`
      }
    });
  } catch (error) {
    console.error("❌ Error generating config:", error);
    return new Response("Error loading config", {
      status: 500,
      headers: {
        "Content-Type": "text/plain"
      }
    });
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
