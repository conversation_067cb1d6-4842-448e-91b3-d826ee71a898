import 'kleur/colors';
import { g as decodeKey } from './chunks/astro/server_CADh2GTU.mjs';
import 'clsx';
import 'cookie';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/astro-designed-error-pages_HupNu_4i.mjs';
import 'es-module-lexer';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/","cacheDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/node_modules/.astro/","outDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/","srcDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/","publicDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/public/","buildClientDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/client/","buildServerDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/server/","adapterName":"@astrojs/vercel","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/generic.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/admin/config.yml","isIndex":false,"type":"endpoint","pattern":"^\\/admin\\/config\\.yml\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"config.yml","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/config.yml.ts","pathname":"/admin/config.yml","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/admin/status.json","isIndex":false,"type":"endpoint","pattern":"^\\/admin\\/status\\.json\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"status.json","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/status.json.ts","pathname":"/admin/status.json","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":"body{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.cms-loading[data-astro-cid-u2h3djql]{display:flex;justify-content:center;align-items:center;height:100vh;background:#f5f5f5}.cms-loading[data-astro-cid-u2h3djql].hidden{display:none}.spinner[data-astro-cid-u2h3djql]{border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:40px;height:40px;animation:spin 2s linear infinite}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n"}],"routeData":{"route":"/admin","isIndex":true,"type":"page","pattern":"^\\/admin\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/index.astro","pathname":"/admin","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/auth","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/auth\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/auth.ts","pathname":"/api/auth","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/callback","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/callback\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"callback","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/callback.ts","pathname":"/api/callback","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":":root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}\n.blog-post[data-astro-cid-4sn4zg3r]{max-width:800px;margin:0 auto}.breadcrumb[data-astro-cid-4sn4zg3r]{margin-bottom:2rem}.back-link[data-astro-cid-4sn4zg3r]{color:var(--primary-color);text-decoration:none;font-weight:500;transition:color .3s ease}.back-link[data-astro-cid-4sn4zg3r]:hover{color:#1d4ed8}.post-header[data-astro-cid-4sn4zg3r]{margin-bottom:3rem}.post-title[data-astro-cid-4sn4zg3r]{font-size:3rem;font-weight:700;margin-bottom:1.5rem;color:var(--text-color);line-height:1.2}.post-meta[data-astro-cid-4sn4zg3r]{display:flex;gap:2rem;margin-bottom:1.5rem;flex-wrap:wrap;color:var(--secondary-color);font-size:.875rem}.meta-item[data-astro-cid-4sn4zg3r]{display:flex;align-items:center;gap:.5rem}.meta-label[data-astro-cid-4sn4zg3r]{font-weight:600;color:var(--text-color)}.post-description[data-astro-cid-4sn4zg3r]{font-size:1.25rem;color:var(--secondary-color);margin-bottom:2rem;line-height:1.6;font-style:italic}.post-tags[data-astro-cid-4sn4zg3r]{display:flex;align-items:center;gap:1rem;flex-wrap:wrap}.tags-label[data-astro-cid-4sn4zg3r]{font-weight:600;color:var(--text-color)}.tags-list[data-astro-cid-4sn4zg3r]{display:flex;flex-wrap:wrap;gap:.5rem}.tag[data-astro-cid-4sn4zg3r]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.875rem;font-weight:500}.post-image[data-astro-cid-4sn4zg3r]{margin-bottom:3rem;border-radius:12px;overflow:hidden;box-shadow:0 10px 25px -3px #0000001a}.post-image[data-astro-cid-4sn4zg3r] img[data-astro-cid-4sn4zg3r]{width:100%;height:auto;display:block}.post-content[data-astro-cid-4sn4zg3r]{line-height:1.7;color:var(--text-color);margin-bottom:3rem}.post-content[data-astro-cid-4sn4zg3r] h2{margin-top:2.5rem;margin-bottom:1rem;font-weight:600;font-size:1.875rem}.post-content[data-astro-cid-4sn4zg3r] h3{margin-top:2rem;margin-bottom:.75rem;font-weight:600;font-size:1.5rem}.post-content[data-astro-cid-4sn4zg3r] h4{margin-top:1.5rem;margin-bottom:.5rem;font-weight:600;font-size:1.25rem}.post-content[data-astro-cid-4sn4zg3r] p{margin-bottom:1.5rem}.post-content[data-astro-cid-4sn4zg3r] ul,.post-content[data-astro-cid-4sn4zg3r] ol{margin-bottom:1.5rem;padding-left:1.5rem}.post-content[data-astro-cid-4sn4zg3r] li{margin-bottom:.5rem}.post-content[data-astro-cid-4sn4zg3r] blockquote{border-left:4px solid var(--primary-color);padding-left:1rem;margin:1.5rem 0;font-style:italic;color:var(--secondary-color)}.post-content[data-astro-cid-4sn4zg3r] code{background:var(--hover-color);padding:.25rem .5rem;border-radius:4px;font-family:Monaco,Menlo,monospace;font-size:.875rem}.post-content[data-astro-cid-4sn4zg3r] pre{background:var(--hover-color);padding:1.5rem;border-radius:8px;overflow-x:auto;margin-bottom:1.5rem}.post-content[data-astro-cid-4sn4zg3r] pre code{background:none;padding:0}.post-content[data-astro-cid-4sn4zg3r] img{max-width:100%;height:auto;border-radius:8px;margin:1.5rem 0}.post-footer[data-astro-cid-4sn4zg3r]{border-top:1px solid var(--border-color);padding-top:2rem}.share-section[data-astro-cid-4sn4zg3r] h3[data-astro-cid-4sn4zg3r]{margin-bottom:1rem;color:var(--text-color);font-weight:600}.share-buttons[data-astro-cid-4sn4zg3r]{display:flex;gap:1rem}.share-button[data-astro-cid-4sn4zg3r]{padding:.5rem 1rem;border-radius:6px;text-decoration:none;font-weight:500;transition:all .3s ease;font-size:.875rem}.share-button[data-astro-cid-4sn4zg3r].twitter{background:#1da1f2;color:#fff}.share-button[data-astro-cid-4sn4zg3r].twitter:hover{background:#1a91da}.share-button[data-astro-cid-4sn4zg3r].linkedin{background:#0077b5;color:#fff}.share-button[data-astro-cid-4sn4zg3r].linkedin:hover{background:#006ba1}@media (max-width: 768px){.post-title[data-astro-cid-4sn4zg3r]{font-size:2.5rem}.post-description[data-astro-cid-4sn4zg3r]{font-size:1.125rem}.post-meta[data-astro-cid-4sn4zg3r]{flex-direction:column;gap:.5rem}.post-tags[data-astro-cid-4sn4zg3r]{flex-direction:column;align-items:flex-start;gap:.75rem}.share-buttons[data-astro-cid-4sn4zg3r]{flex-direction:column}.share-button[data-astro-cid-4sn4zg3r]{text-align:center}}\n"}],"routeData":{"route":"/blog/[slug]","isIndex":false,"type":"page","pattern":"^\\/blog\\/([^/]+?)\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/blog/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":":root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}\n.blog-card[data-astro-cid-e3grugc2]{background:#fff;border-radius:12px;box-shadow:0 4px 6px -1px #0000001a;overflow:hidden;transition:transform .3s ease,box-shadow .3s ease;height:100%;display:flex;flex-direction:column}.blog-card[data-astro-cid-e3grugc2]:hover{transform:translateY(-4px);box-shadow:0 10px 25px -3px #0000001a}.blog-card[data-astro-cid-e3grugc2].featured{border:2px solid var(--primary-color)}.blog-image[data-astro-cid-e3grugc2]{aspect-ratio:16/9;overflow:hidden}.blog-image[data-astro-cid-e3grugc2] img[data-astro-cid-e3grugc2]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.blog-card[data-astro-cid-e3grugc2]:hover .blog-image[data-astro-cid-e3grugc2] img[data-astro-cid-e3grugc2]{transform:scale(1.05)}.blog-content[data-astro-cid-e3grugc2]{padding:1.5rem;flex:1;display:flex;flex-direction:column}.blog-meta[data-astro-cid-e3grugc2]{margin-bottom:.75rem}.blog-meta[data-astro-cid-e3grugc2] time[data-astro-cid-e3grugc2]{color:var(--secondary-color);font-size:.875rem;font-weight:500}.blog-title[data-astro-cid-e3grugc2]{margin:0 0 .75rem;font-size:1.25rem;font-weight:600;line-height:1.4}.blog-title[data-astro-cid-e3grugc2] a[data-astro-cid-e3grugc2]{color:var(--text-color);text-decoration:none;transition:color .3s ease}.blog-title[data-astro-cid-e3grugc2] a[data-astro-cid-e3grugc2]:hover{color:var(--primary-color)}.blog-description[data-astro-cid-e3grugc2]{color:var(--secondary-color);margin-bottom:1rem;flex:1;line-height:1.6}.blog-tags[data-astro-cid-e3grugc2]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:1.5rem}.tag[data-astro-cid-e3grugc2]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.75rem;font-weight:500}.read-more[data-astro-cid-e3grugc2]{color:var(--primary-color);text-decoration:none;font-weight:600;font-size:.875rem;transition:color .3s ease;align-self:flex-start}.read-more[data-astro-cid-e3grugc2]:hover{color:#1d4ed8}@media (max-width: 768px){.blog-content[data-astro-cid-e3grugc2]{padding:1rem}}\n.blog-header[data-astro-cid-ijnerlr2]{text-align:center;margin-bottom:4rem}.page-title[data-astro-cid-ijnerlr2]{font-size:3rem;font-weight:700;margin-bottom:1rem;color:var(--text-color)}.page-description[data-astro-cid-ijnerlr2]{font-size:1.25rem;color:var(--secondary-color);max-width:600px;margin:0 auto;line-height:1.6}.blog-container[data-astro-cid-ijnerlr2]{max-width:1200px;margin:0 auto}.filters[data-astro-cid-ijnerlr2]{margin-bottom:3rem;text-align:center}.filters[data-astro-cid-ijnerlr2] h3[data-astro-cid-ijnerlr2]{margin-bottom:1rem;color:var(--text-color);font-weight:600}.filter-tags[data-astro-cid-ijnerlr2]{display:flex;flex-wrap:wrap;gap:.75rem;justify-content:center}.filter-tag[data-astro-cid-ijnerlr2]{background:var(--hover-color);border:1px solid var(--border-color);color:var(--secondary-color);padding:.5rem 1rem;border-radius:25px;cursor:pointer;transition:all .3s ease;font-weight:500}.filter-tag[data-astro-cid-ijnerlr2]:hover,.filter-tag[data-astro-cid-ijnerlr2].active{background:var(--primary-color);color:#fff;border-color:var(--primary-color)}.posts-grid[data-astro-cid-ijnerlr2]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem}.post-item[data-astro-cid-ijnerlr2]{transition:opacity .3s ease,transform .3s ease}.post-item[data-astro-cid-ijnerlr2].hidden{opacity:0;transform:scale(.95);pointer-events:none}.empty-state[data-astro-cid-ijnerlr2]{text-align:center;padding:4rem 2rem;color:var(--secondary-color)}.empty-state[data-astro-cid-ijnerlr2] h2[data-astro-cid-ijnerlr2]{font-size:2rem;margin-bottom:1rem;color:var(--text-color)}@media (max-width: 768px){.page-title[data-astro-cid-ijnerlr2]{font-size:2.5rem}.page-description[data-astro-cid-ijnerlr2]{font-size:1.125rem}.posts-grid[data-astro-cid-ijnerlr2]{grid-template-columns:1fr;gap:1.5rem}.filter-tags[data-astro-cid-ijnerlr2]{gap:.5rem}.filter-tag[data-astro-cid-ijnerlr2]{padding:.375rem .75rem;font-size:.875rem}}\n"}],"routeData":{"route":"/blog","isIndex":false,"type":"page","pattern":"^\\/blog\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog.astro","pathname":"/blog","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/debug-auth","isIndex":false,"type":"endpoint","pattern":"^\\/debug-auth\\/?$","segments":[[{"content":"debug-auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/debug-auth.ts","pathname":"/debug-auth","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":":root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}\n"},{"type":"external","src":"/_astro/_slug_.DSYtqgOx.css"}],"routeData":{"route":"/projects/[slug]","isIndex":false,"type":"page","pattern":"^\\/projects\\/([^/]+?)\\/?$","segments":[[{"content":"projects","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/projects/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":":root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}\n.project-card[data-astro-cid-mspuyifq]{background:#fff;border-radius:12px;box-shadow:0 4px 6px -1px #0000001a;overflow:hidden;transition:transform .3s ease,box-shadow .3s ease;height:100%;display:flex;flex-direction:column}.project-card[data-astro-cid-mspuyifq]:hover{transform:translateY(-4px);box-shadow:0 10px 25px -3px #0000001a}.project-card[data-astro-cid-mspuyifq].featured{border:2px solid var(--primary-color)}.project-image[data-astro-cid-mspuyifq]{aspect-ratio:16/9;overflow:hidden}.project-image[data-astro-cid-mspuyifq] img[data-astro-cid-mspuyifq]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.project-card[data-astro-cid-mspuyifq]:hover .project-image[data-astro-cid-mspuyifq] img[data-astro-cid-mspuyifq]{transform:scale(1.05)}.project-content[data-astro-cid-mspuyifq]{padding:1.5rem;flex:1;display:flex;flex-direction:column}.project-title[data-astro-cid-mspuyifq]{margin:0 0 .75rem;font-size:1.25rem;font-weight:600}.project-title[data-astro-cid-mspuyifq] a[data-astro-cid-mspuyifq]{color:var(--text-color);text-decoration:none;transition:color .3s ease}.project-title[data-astro-cid-mspuyifq] a[data-astro-cid-mspuyifq]:hover{color:var(--primary-color)}.project-description[data-astro-cid-mspuyifq]{color:var(--secondary-color);margin-bottom:1rem;flex:1}.project-technologies[data-astro-cid-mspuyifq]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:1.5rem}.tech-tag[data-astro-cid-mspuyifq]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.875rem;font-weight:500}.project-links[data-astro-cid-mspuyifq]{display:flex;gap:.75rem;flex-wrap:wrap}.project-link[data-astro-cid-mspuyifq]{padding:.5rem 1rem;border-radius:6px;text-decoration:none;font-weight:500;font-size:.875rem;transition:all .3s ease;text-align:center;flex:1;min-width:fit-content}.project-link[data-astro-cid-mspuyifq].live{background:var(--primary-color);color:#fff}.project-link[data-astro-cid-mspuyifq].live:hover{background:#1d4ed8}.project-link[data-astro-cid-mspuyifq].github{background:#24292e;color:#fff}.project-link[data-astro-cid-mspuyifq].github:hover{background:#1a1e22}.project-link[data-astro-cid-mspuyifq].details{background:var(--hover-color);color:var(--text-color);border:1px solid var(--border-color)}.project-link[data-astro-cid-mspuyifq].details:hover{background:var(--border-color)}@media (max-width: 768px){.project-links[data-astro-cid-mspuyifq]{flex-direction:column}.project-link[data-astro-cid-mspuyifq]{flex:none}}\n.projects-header[data-astro-cid-aid3sr62]{text-align:center;margin-bottom:4rem}.page-title[data-astro-cid-aid3sr62]{font-size:3rem;font-weight:700;margin-bottom:1rem;color:var(--text-color)}.page-description[data-astro-cid-aid3sr62]{font-size:1.25rem;color:var(--secondary-color);max-width:600px;margin:0 auto;line-height:1.6}.projects-container[data-astro-cid-aid3sr62]{max-width:1200px;margin:0 auto}.filters[data-astro-cid-aid3sr62]{margin-bottom:3rem;text-align:center}.filters[data-astro-cid-aid3sr62] h3[data-astro-cid-aid3sr62]{margin-bottom:1rem;color:var(--text-color);font-weight:600}.filter-tags[data-astro-cid-aid3sr62]{display:flex;flex-wrap:wrap;gap:.75rem;justify-content:center}.filter-tag[data-astro-cid-aid3sr62]{background:var(--hover-color);border:1px solid var(--border-color);color:var(--secondary-color);padding:.5rem 1rem;border-radius:25px;cursor:pointer;transition:all .3s ease;font-weight:500}.filter-tag[data-astro-cid-aid3sr62]:hover,.filter-tag[data-astro-cid-aid3sr62].active{background:var(--primary-color);color:#fff;border-color:var(--primary-color)}.projects-grid[data-astro-cid-aid3sr62]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem}.project-item[data-astro-cid-aid3sr62]{transition:opacity .3s ease,transform .3s ease}.project-item[data-astro-cid-aid3sr62].hidden{opacity:0;transform:scale(.95);pointer-events:none}.empty-state[data-astro-cid-aid3sr62]{text-align:center;padding:4rem 2rem;color:var(--secondary-color)}.empty-state[data-astro-cid-aid3sr62] h2[data-astro-cid-aid3sr62]{font-size:2rem;margin-bottom:1rem;color:var(--text-color)}@media (max-width: 768px){.page-title[data-astro-cid-aid3sr62]{font-size:2.5rem}.page-description[data-astro-cid-aid3sr62]{font-size:1.125rem}.projects-grid[data-astro-cid-aid3sr62]{grid-template-columns:1fr;gap:1.5rem}.filter-tags[data-astro-cid-aid3sr62]{gap:.5rem}.filter-tag[data-astro-cid-aid3sr62]{padding:.375rem .75rem;font-size:.875rem}}\n"}],"routeData":{"route":"/projects","isIndex":false,"type":"page","pattern":"^\\/projects\\/?$","segments":[[{"content":"projects","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/projects.astro","pathname":"/projects","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-auth","isIndex":false,"type":"endpoint","pattern":"^\\/test-auth\\/?$","segments":[[{"content":"test-auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-auth.ts","pathname":"/test-auth","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-callback-success","isIndex":false,"type":"endpoint","pattern":"^\\/test-callback-success\\/?$","segments":[[{"content":"test-callback-success","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-callback-success.ts","pathname":"/test-callback-success","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-config","isIndex":false,"type":"endpoint","pattern":"^\\/test-config\\/?$","segments":[[{"content":"test-config","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-config.ts","pathname":"/test-config","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-oauth-flow","isIndex":false,"type":"endpoint","pattern":"^\\/test-oauth-flow\\/?$","segments":[[{"content":"test-oauth-flow","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-oauth-flow.ts","pathname":"/test-oauth-flow","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-oauth-flow-complete","isIndex":false,"type":"endpoint","pattern":"^\\/test-oauth-flow-complete\\/?$","segments":[[{"content":"test-oauth-flow-complete","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-oauth-flow-complete.ts","pathname":"/test-oauth-flow-complete","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-oauth-simple","isIndex":false,"type":"endpoint","pattern":"^\\/test-oauth-simple\\/?$","segments":[[{"content":"test-oauth-simple","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-oauth-simple.ts","pathname":"/test-oauth-simple","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/test-postmessage","isIndex":false,"type":"endpoint","pattern":"^\\/test-postmessage\\/?$","segments":[[{"content":"test-postmessage","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-postmessage.ts","pathname":"/test-postmessage","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":":root{--primary-color: #2563eb;--secondary-color: #64748b;--text-color: #1e293b;--bg-color: #ffffff;--border-color: #e2e8f0;--hover-color: #f8fafc}[data-astro-cid-sckkx6r4]{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;line-height:1.6;color:var(--text-color);background-color:var(--bg-color)}.navbar[data-astro-cid-sckkx6r4]{background:var(--bg-color);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:100}.nav-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:1rem 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[data-astro-cid-sckkx6r4]{font-size:1.5rem;font-weight:700;color:var(--primary-color);text-decoration:none}.nav-menu[data-astro-cid-sckkx6r4]{display:flex;list-style:none;gap:2rem}.nav-link[data-astro-cid-sckkx6r4]{color:var(--text-color);text-decoration:none;font-weight:500;transition:color .3s ease}.nav-link[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}main[data-astro-cid-sckkx6r4]{min-height:calc(100vh - 140px);max-width:1200px;margin:0 auto;padding:2rem}.footer[data-astro-cid-sckkx6r4]{background:var(--hover-color);border-top:1px solid var(--border-color);margin-top:4rem}.footer-container[data-astro-cid-sckkx6r4]{max-width:1200px;margin:0 auto;padding:2rem;display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.social-links[data-astro-cid-sckkx6r4]{display:flex;gap:1rem}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]{color:var(--secondary-color);text-decoration:none;transition:color .3s ease}.social-links[data-astro-cid-sckkx6r4] a[data-astro-cid-sckkx6r4]:hover{color:var(--primary-color)}@media (max-width: 768px){.nav-container[data-astro-cid-sckkx6r4]{padding:1rem}.nav-menu[data-astro-cid-sckkx6r4]{gap:1rem}main[data-astro-cid-sckkx6r4]{padding:1rem}.footer-container[data-astro-cid-sckkx6r4]{flex-direction:column;text-align:center}}\n.project-card[data-astro-cid-mspuyifq]{background:#fff;border-radius:12px;box-shadow:0 4px 6px -1px #0000001a;overflow:hidden;transition:transform .3s ease,box-shadow .3s ease;height:100%;display:flex;flex-direction:column}.project-card[data-astro-cid-mspuyifq]:hover{transform:translateY(-4px);box-shadow:0 10px 25px -3px #0000001a}.project-card[data-astro-cid-mspuyifq].featured{border:2px solid var(--primary-color)}.project-image[data-astro-cid-mspuyifq]{aspect-ratio:16/9;overflow:hidden}.project-image[data-astro-cid-mspuyifq] img[data-astro-cid-mspuyifq]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.project-card[data-astro-cid-mspuyifq]:hover .project-image[data-astro-cid-mspuyifq] img[data-astro-cid-mspuyifq]{transform:scale(1.05)}.project-content[data-astro-cid-mspuyifq]{padding:1.5rem;flex:1;display:flex;flex-direction:column}.project-title[data-astro-cid-mspuyifq]{margin:0 0 .75rem;font-size:1.25rem;font-weight:600}.project-title[data-astro-cid-mspuyifq] a[data-astro-cid-mspuyifq]{color:var(--text-color);text-decoration:none;transition:color .3s ease}.project-title[data-astro-cid-mspuyifq] a[data-astro-cid-mspuyifq]:hover{color:var(--primary-color)}.project-description[data-astro-cid-mspuyifq]{color:var(--secondary-color);margin-bottom:1rem;flex:1}.project-technologies[data-astro-cid-mspuyifq]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:1.5rem}.tech-tag[data-astro-cid-mspuyifq]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.875rem;font-weight:500}.project-links[data-astro-cid-mspuyifq]{display:flex;gap:.75rem;flex-wrap:wrap}.project-link[data-astro-cid-mspuyifq]{padding:.5rem 1rem;border-radius:6px;text-decoration:none;font-weight:500;font-size:.875rem;transition:all .3s ease;text-align:center;flex:1;min-width:fit-content}.project-link[data-astro-cid-mspuyifq].live{background:var(--primary-color);color:#fff}.project-link[data-astro-cid-mspuyifq].live:hover{background:#1d4ed8}.project-link[data-astro-cid-mspuyifq].github{background:#24292e;color:#fff}.project-link[data-astro-cid-mspuyifq].github:hover{background:#1a1e22}.project-link[data-astro-cid-mspuyifq].details{background:var(--hover-color);color:var(--text-color);border:1px solid var(--border-color)}.project-link[data-astro-cid-mspuyifq].details:hover{background:var(--border-color)}@media (max-width: 768px){.project-links[data-astro-cid-mspuyifq]{flex-direction:column}.project-link[data-astro-cid-mspuyifq]{flex:none}}\n.blog-card[data-astro-cid-e3grugc2]{background:#fff;border-radius:12px;box-shadow:0 4px 6px -1px #0000001a;overflow:hidden;transition:transform .3s ease,box-shadow .3s ease;height:100%;display:flex;flex-direction:column}.blog-card[data-astro-cid-e3grugc2]:hover{transform:translateY(-4px);box-shadow:0 10px 25px -3px #0000001a}.blog-card[data-astro-cid-e3grugc2].featured{border:2px solid var(--primary-color)}.blog-image[data-astro-cid-e3grugc2]{aspect-ratio:16/9;overflow:hidden}.blog-image[data-astro-cid-e3grugc2] img[data-astro-cid-e3grugc2]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.blog-card[data-astro-cid-e3grugc2]:hover .blog-image[data-astro-cid-e3grugc2] img[data-astro-cid-e3grugc2]{transform:scale(1.05)}.blog-content[data-astro-cid-e3grugc2]{padding:1.5rem;flex:1;display:flex;flex-direction:column}.blog-meta[data-astro-cid-e3grugc2]{margin-bottom:.75rem}.blog-meta[data-astro-cid-e3grugc2] time[data-astro-cid-e3grugc2]{color:var(--secondary-color);font-size:.875rem;font-weight:500}.blog-title[data-astro-cid-e3grugc2]{margin:0 0 .75rem;font-size:1.25rem;font-weight:600;line-height:1.4}.blog-title[data-astro-cid-e3grugc2] a[data-astro-cid-e3grugc2]{color:var(--text-color);text-decoration:none;transition:color .3s ease}.blog-title[data-astro-cid-e3grugc2] a[data-astro-cid-e3grugc2]:hover{color:var(--primary-color)}.blog-description[data-astro-cid-e3grugc2]{color:var(--secondary-color);margin-bottom:1rem;flex:1;line-height:1.6}.blog-tags[data-astro-cid-e3grugc2]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:1.5rem}.tag[data-astro-cid-e3grugc2]{background:var(--hover-color);color:var(--secondary-color);padding:.25rem .75rem;border-radius:20px;font-size:.75rem;font-weight:500}.read-more[data-astro-cid-e3grugc2]{color:var(--primary-color);text-decoration:none;font-weight:600;font-size:.875rem;transition:color .3s ease;align-self:flex-start}.read-more[data-astro-cid-e3grugc2]:hover{color:#1d4ed8}@media (max-width: 768px){.blog-content[data-astro-cid-e3grugc2]{padding:1rem}}\n.hero[data-astro-cid-j7pv25f6]{text-align:center;padding:4rem 0 6rem;max-width:800px;margin:0 auto}.hero-title[data-astro-cid-j7pv25f6]{font-size:3.5rem;font-weight:700;margin-bottom:1rem;line-height:1.2}.highlight[data-astro-cid-j7pv25f6]{color:var(--primary-color)}.hero-subtitle[data-astro-cid-j7pv25f6]{font-size:1.5rem;color:var(--secondary-color);margin-bottom:1.5rem;font-weight:500}.hero-description[data-astro-cid-j7pv25f6]{font-size:1.125rem;color:var(--secondary-color);margin-bottom:2.5rem;line-height:1.7}.hero-actions[data-astro-cid-j7pv25f6]{display:flex;gap:1rem;justify-content:center;flex-wrap:wrap}.btn[data-astro-cid-j7pv25f6]{padding:.875rem 2rem;border-radius:8px;text-decoration:none;font-weight:600;transition:all .3s ease;display:inline-block}.btn-primary[data-astro-cid-j7pv25f6]{background:var(--primary-color);color:#fff}.btn-primary[data-astro-cid-j7pv25f6]:hover{background:#1d4ed8;transform:translateY(-2px)}.btn-secondary[data-astro-cid-j7pv25f6]{background:transparent;color:var(--primary-color);border:2px solid var(--primary-color)}.btn-secondary[data-astro-cid-j7pv25f6]:hover{background:var(--primary-color);color:#fff;transform:translateY(-2px)}.section-title[data-astro-cid-j7pv25f6]{font-size:2.5rem;font-weight:700;text-align:center;margin-bottom:3rem;color:var(--text-color)}.featured-projects[data-astro-cid-j7pv25f6],.recent-posts[data-astro-cid-j7pv25f6]{margin-bottom:6rem}.projects-grid[data-astro-cid-j7pv25f6],.posts-grid[data-astro-cid-j7pv25f6]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem;margin-bottom:3rem}.section-footer[data-astro-cid-j7pv25f6]{text-align:center}.view-all-link[data-astro-cid-j7pv25f6]{color:var(--primary-color);text-decoration:none;font-weight:600;font-size:1.125rem;transition:color .3s ease}.view-all-link[data-astro-cid-j7pv25f6]:hover{color:#1d4ed8}@media (max-width: 768px){.hero[data-astro-cid-j7pv25f6]{padding:2rem 0 4rem}.hero-title[data-astro-cid-j7pv25f6]{font-size:2.5rem}.hero-subtitle[data-astro-cid-j7pv25f6]{font-size:1.25rem}.hero-description[data-astro-cid-j7pv25f6]{font-size:1rem}.hero-actions[data-astro-cid-j7pv25f6]{flex-direction:column;align-items:center}.btn[data-astro-cid-j7pv25f6]{width:100%;max-width:300px}.section-title[data-astro-cid-j7pv25f6]{font-size:2rem}.projects-grid[data-astro-cid-j7pv25f6],.posts-grid[data-astro-cid-j7pv25f6]{grid-template-columns:1fr;gap:1.5rem}}\n"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://bagusfarisa-astro.vercel.app","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog/[slug].astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/index.astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects/[slug].astro",{"propagation":"in-tree","containsHead":true}],["\u0000astro:content",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/blog@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astrojs-ssr-virtual-entry",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/blog/[slug]@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/index@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/projects@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/projects/[slug]@_@astro",{"propagation":"in-tree","containsHead":false}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000noop-middleware":"_noop-middleware.mjs","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astro-page:src/pages/admin/config.yml@_@ts":"pages/admin/config.yml.astro.mjs","\u0000@astro-page:src/pages/admin/status.json@_@ts":"pages/admin/status.json.astro.mjs","\u0000@astro-page:src/pages/admin/index@_@astro":"pages/admin.astro.mjs","\u0000@astro-page:src/pages/api/auth@_@ts":"pages/api/auth.astro.mjs","\u0000@astro-page:src/pages/api/callback@_@ts":"pages/api/callback.astro.mjs","\u0000@astro-page:src/pages/blog/[slug]@_@astro":"pages/blog/_slug_.astro.mjs","\u0000@astro-page:src/pages/blog@_@astro":"pages/blog.astro.mjs","\u0000@astro-page:src/pages/debug-auth@_@ts":"pages/debug-auth.astro.mjs","\u0000@astro-page:src/pages/projects/[slug]@_@astro":"pages/projects/_slug_.astro.mjs","\u0000@astro-page:src/pages/projects@_@astro":"pages/projects.astro.mjs","\u0000@astro-page:src/pages/test-auth@_@ts":"pages/test-auth.astro.mjs","\u0000@astro-page:src/pages/test-callback-success@_@ts":"pages/test-callback-success.astro.mjs","\u0000@astro-page:src/pages/test-config@_@ts":"pages/test-config.astro.mjs","\u0000@astro-page:src/pages/test-oauth-flow@_@ts":"pages/test-oauth-flow.astro.mjs","\u0000@astro-page:src/pages/test-oauth-flow-complete@_@ts":"pages/test-oauth-flow-complete.astro.mjs","\u0000@astro-page:src/pages/test-oauth-simple@_@ts":"pages/test-oauth-simple.astro.mjs","\u0000@astro-page:src/pages/test-postmessage@_@ts":"pages/test-postmessage.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"pages/_image.astro.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_BUjJkJ0t.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/.astro/content-assets.mjs":"chunks/content-assets_DleWbedO.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/.astro/content-modules.mjs":"chunks/content-modules_Dz-S_Wwv.mjs","\u0000astro:data-layer-content":"chunks/_astro_data-layer-content_DLw1WwiG.mjs","\u0000@astrojs-manifest":"manifest_tDd5MoFh.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro?astro&type=script&index=0&lang.ts":"_astro/blog.astro_astro_type_script_index_0_lang.a6uhtcI5.js","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro?astro&type=script&index=0&lang.ts":"_astro/projects.astro_astro_type_script_index_0_lang.UQqBg1RE.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",function(){const e=document.querySelectorAll(\".filter-tag\"),c=document.querySelectorAll(\".post-item\");e.forEach(i=>{i.addEventListener(\"click\",function(){e.forEach(t=>t.classList.remove(\"active\")),this.classList.add(\"active\");const s=this.getAttribute(\"data-filter\");c.forEach(t=>{if(s===\"all\")t.classList.remove(\"hidden\");else{const a=t.getAttribute(\"data-tags\");a&&a.includes(s)?t.classList.remove(\"hidden\"):t.classList.add(\"hidden\")}})})})});"],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",function(){const t=document.querySelectorAll(\".filter-tag\"),i=document.querySelectorAll(\".project-item\");t.forEach(o=>{o.addEventListener(\"click\",function(){t.forEach(e=>e.classList.remove(\"active\")),this.classList.add(\"active\");const s=this.getAttribute(\"data-filter\");i.forEach(e=>{if(s===\"all\")e.classList.remove(\"hidden\");else{const c=e.getAttribute(\"data-technologies\");c&&c.includes(s)?e.classList.remove(\"hidden\"):e.classList.add(\"hidden\")}})})})});"]],"assets":["/_astro/_slug_.DSYtqgOx.css","/favicon.svg"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"C3p2A9kQcJthYxQ022/DjRllcy7MyRgRCrLpGHLjPvM="});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = null;

export { manifest };
