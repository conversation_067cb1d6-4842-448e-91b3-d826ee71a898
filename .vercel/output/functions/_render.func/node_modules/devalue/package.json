{"name": "devalue", "description": "Gets the job done when JSON.stringify can't", "version": "5.1.1", "repository": "<PERSON>-<PERSON>/devalue", "sideEffects": false, "exports": {".": {"types": "./types/index.d.ts", "import": "./index.js", "default": "./index.js"}}, "files": ["index.js", "src", "types"], "types": "./types/index.d.ts", "devDependencies": {"dts-buddy": "^0.0.4", "publint": "^0.1.7", "typescript": "^3.1.3", "uvu": "^0.5.6"}, "scripts": {"build": "dts-buddy", "test": "uvu test", "prepublishOnly": "npm test && npm run build && publint"}, "license": "MIT", "type": "module", "packageManager": "pnpm@8.5.1"}