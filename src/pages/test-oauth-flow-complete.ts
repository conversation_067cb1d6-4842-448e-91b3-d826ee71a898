import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Complete OAuth Flow Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
    .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
    .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
  </style>
</head>
<body>
  <h1>Complete OAuth Flow Test</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}<br>
    <strong>Purpose:</strong> Test the complete OAuth flow including postMessage communication
  </div>
  
  <div class="test-section">
    <h2>OAuth Flow Test</h2>
    <div class="step">
      <strong>Step 1:</strong> Click "Start OAuth Flow" to open the auth popup
    </div>
    <div class="step">
      <strong>Step 2:</strong> The popup will redirect to GitHub OAuth
    </div>
    <div class="step">
      <strong>Step 3:</strong> After GitHub auth, it redirects to our callback
    </div>
    <div class="step">
      <strong>Step 4:</strong> Callback sends postMessage back to this window
    </div>
    <div class="step">
      <strong>Step 5:</strong> This window receives the message and logs it
    </div>
    
    <button onclick="startOAuthFlow()">Start OAuth Flow</button>
    <button onclick="testWithMockCallback()">Test with Mock Callback</button>
    <button onclick="clearLog()">Clear Log</button>
  </div>
  
  <div class="test-section">
    <h2>Test Results</h2>
    <div id="log"></div>
  </div>

  <script>
    let popup = null;
    let testStartTime = null;

    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const div = document.createElement('div');
      div.className = 'status ' + type;
      const timestamp = new Date().toLocaleTimeString();
      const elapsed = testStartTime ? ((Date.now() - testStartTime) / 1000).toFixed(1) + 's' : '';
      div.innerHTML = '<strong>' + timestamp + (elapsed ? ' (+' + elapsed + ')' : '') + ':</strong> ' + message;
      logDiv.appendChild(div);
      console.log(message);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
      testStartTime = null;
    }

    // Listen for messages from popup
    window.addEventListener('message', function(event) {
      log('📨 Received message from: ' + event.origin);
      log('📝 Message data: ' + JSON.stringify(event.data));
      
      if (typeof event.data === 'string' && event.data.startsWith('authorization:github:')) {
        if (event.data.includes(':success:')) {
          log('✅ OAuth Success Message Received!', 'success');
          try {
            const tokenData = JSON.parse(event.data.split(':success:')[1]);
            log('🔑 Token: ' + (tokenData.token ? 'Present (' + tokenData.token.substring(0, 10) + '...)' : 'Missing'), tokenData.token ? 'success' : 'error');
            log('🏷️ Provider: ' + tokenData.provider, 'success');
            log('🎉 OAuth flow completed successfully!', 'success');
          } catch (error) {
            log('❌ Error parsing token data: ' + error.message, 'error');
          }
        } else if (event.data.includes(':error:')) {
          log('❌ OAuth Error Message Received!', 'error');
          try {
            const errorData = JSON.parse(event.data.split(':error:')[1]);
            log('Error: ' + errorData.error, 'error');
          } catch (error) {
            log('❌ Error parsing error data: ' + error.message, 'error');
          }
        }
      } else {
        log('📝 Other message: ' + JSON.stringify(event.data));
      }
    });

    function startOAuthFlow() {
      testStartTime = Date.now();
      log('🚀 Starting OAuth flow test...');
      
      if (popup && !popup.closed) {
        popup.close();
        log('🔒 Closed existing popup');
      }
      
      log('🔗 Opening auth endpoint: /api/auth');
      popup = window.open('/api/auth', 'oauth-test', 'width=600,height=700,scrollbars=yes,resizable=yes');
      
      if (popup) {
        log('✅ Popup opened successfully', 'success');
        
        // Monitor popup
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            log('🔒 Popup was closed');
            clearInterval(checkClosed);
          }
        }, 1000);
        
        // Timeout after 2 minutes
        setTimeout(() => {
          if (popup && !popup.closed) {
            log('⏰ OAuth flow timeout (2 minutes)', 'warning');
            popup.close();
          }
        }, 120000);
      } else {
        log('❌ Failed to open popup (blocked by browser?)', 'error');
      }
    }

    function testWithMockCallback() {
      testStartTime = Date.now();
      log('🧪 Testing with mock callback...');
      
      if (popup && !popup.closed) {
        popup.close();
      }
      
      popup = window.open('/test-callback-success', 'mock-test', 'width=600,height=600');
      
      if (popup) {
        log('✅ Mock callback popup opened', 'success');
      } else {
        log('❌ Failed to open mock callback popup', 'error');
      }
    }

    log('🎯 OAuth flow test page loaded');
    log('👂 Listening for postMessage events...');
  </script>
</body>
</html>
  `;

  return new Response(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
};
