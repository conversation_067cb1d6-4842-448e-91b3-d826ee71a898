import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const status = {
    timestamp: new Date().toISOString(),
    environment: url.hostname === 'localhost' || url.hostname === '127.0.0.1' ? 'development' : 'production',
    origin: url.origin,
    configEndpoint: '/admin/config.yml',
    authEndpoint: '/api/auth',
    callbackEndpoint: '/api/callback',
    adminPath: '/admin/',
    cmsVersion: '3.0.0+',
    status: 'healthy'
  };

  return new Response(JSON.stringify(status, null, 2), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache'
    }
  });
};
