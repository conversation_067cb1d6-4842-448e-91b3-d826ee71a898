import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>PostMessage Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
    .popup-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
  </style>
</head>
<body>
  <h1>PostMessage Communication Test</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}
  </div>
  
  <div class="popup-container">
    <h2>Test OAuth Popup Communication</h2>
    <button onclick="testOAuthPopup()">Open OAuth Test Popup</button>
    <button onclick="testCallbackDirect()">Test Callback Direct</button>
    <button onclick="clearLog()">Clear Log</button>
  </div>
  
  <h2>Message Log</h2>
  <div id="log"></div>

  <script>
    let popup = null;

    function log(message, type = 'info') {
      const logDiv = document.getElementById('log');
      const div = document.createElement('div');
      div.className = 'status ' + type;
      div.innerHTML = '<strong>' + new Date().toLocaleTimeString() + ':</strong> ' + message;
      logDiv.appendChild(div);
      console.log(message);
    }

    function clearLog() {
      document.getElementById('log').innerHTML = '';
    }

    // Listen for messages from popup
    window.addEventListener('message', function(event) {
      log('📨 Received message from: ' + event.origin);
      log('📝 Message data: ' + JSON.stringify(event.data));
      
      if (typeof event.data === 'string' && event.data.startsWith('authorization:github:')) {
        if (event.data.includes(':success:')) {
          log('✅ OAuth Success Message Received!', 'success');
          try {
            const tokenData = JSON.parse(event.data.split(':success:')[1]);
            log('🔑 Token: ' + (tokenData.token ? 'Present' : 'Missing'), tokenData.token ? 'success' : 'error');
            log('🏷️ Provider: ' + tokenData.provider);
          } catch (error) {
            log('❌ Error parsing token data: ' + error.message, 'error');
          }
        } else if (event.data.includes(':error:')) {
          log('❌ OAuth Error Message Received!', 'error');
          try {
            const errorData = JSON.parse(event.data.split(':error:')[1]);
            log('Error: ' + errorData.error, 'error');
          } catch (error) {
            log('❌ Error parsing error data: ' + error.message, 'error');
          }
        }
      }
    });

    function testOAuthPopup() {
      log('🚀 Opening OAuth popup...');
      
      if (popup && !popup.closed) {
        popup.close();
      }
      
      popup = window.open('/api/auth', 'oauth-test', 'width=600,height=600,scrollbars=yes,resizable=yes');
      
      if (popup) {
        log('✅ Popup opened successfully');
        
        // Monitor popup
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            log('🔒 Popup was closed');
            clearInterval(checkClosed);
          }
        }, 1000);
      } else {
        log('❌ Failed to open popup (blocked?)', 'error');
      }
    }

    function testCallbackDirect() {
      log('🔗 Opening callback page directly...');
      window.open('/api/callback?code=test_code', 'callback-test', 'width=600,height=600');
    }

    log('🎯 PostMessage test page loaded');
    log('👂 Listening for messages...');
  </script>
</body>
</html>
  `;

  return new Response(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
};
