// @ts-check
import { defineConfig } from 'astro/config';
import vercel from '@astrojs/vercel';

// https://astro.build/config
export default defineConfig({
  site: 'https://bagusfarisa-astro.vercel.app',
  output: 'server', // Changed from 'static' to 'server' to support API routes
  adapter: vercel({
    webAnalytics: { enabled: true }
  }),
  build: {
    format: 'directory'
  },
  vite: {
    optimizeDeps: {
      exclude: ['@astrojs/markdown-remark']
    }
  }
});
